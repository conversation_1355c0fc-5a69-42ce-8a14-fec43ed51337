{layout name="layout1" /}
<div class="layui-fluid">
    <div class="layui-card">
    <div class="layui-card-header">
        <span>模板配置管理</span>
        <div class="layui-btn-group fr">
            <a class="layui-btn layui-btn-sm" href="{:url('poster_template/config_add')}">
                <i class="layui-icon layui-icon-add-1"></i>添加配置
            </a>
        </div>
    </div>
    <div class="layui-card-body">
        <!-- 搜索表单 -->
        <form class="layui-form" lay-filter="search-form">
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md3">
                    <input type="text" name="keyword" placeholder="搜索配置名称/模板标题" class="layui-input">
                </div>
                <div class="layui-col-md2">
                    <select name="status">
                        <option value="">全部状态</option>
                        <option value="1">启用</option>
                        <option value="0">禁用</option>
                    </select>
                </div>
                <div class="layui-col-md2">
                    <input type="text" name="template_id" placeholder="模板ID" class="layui-input">
                </div>
                <div class="layui-col-md2">
                    <button class="layui-btn" lay-submit lay-filter="search">搜索</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>

        <!-- 数据表格 -->
        <table class="layui-hide" id="config-table" lay-filter="config-table"></table>
    </div>
</div>

<!-- 表格工具栏 -->
<script type="text/html" id="table-toolbar">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm" lay-event="refresh">
            <i class="layui-icon layui-icon-refresh"></i>刷新
        </button>
    </div>
</script>

<!-- 行工具栏 -->
<script type="text/html" id="row-toolbar">
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="copy">复制</a>
    <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="preview">预览</a>
    {{# if(d.status == 1) { }}
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="disable">禁用</a>
    {{# } else { }}
    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="enable">启用</a>
    {{# } }}
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">删除</a>
</script>

<!-- 状态模板 -->
<script type="text/html" id="status-tpl">
    {{# if(d.status == 1) { }}
    <span class="layui-badge layui-bg-green">启用</span>
    {{# } else { }}
    <span class="layui-badge">禁用</span>
    {{# } }}
</script>

<!-- 参数统计模板 -->
<script type="text/html" id="param-stats-tpl">
    <div class="param-stats">
        <span class="layui-badge layui-bg-blue">总数: {{d.parameter_stats.total}}</span>
        <span class="layui-badge layui-bg-green">启用: {{d.parameter_stats.enabled}}</span>
        <span class="layui-badge layui-bg-orange">必填: {{d.parameter_stats.required}}</span>
    </div>
</script>

{/block}

{block name="script"}
<script>
layui.use(['table', 'form', 'layer'], function(){
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;

    // 渲染表格
    var tableIns = table.render({
        elem: '#config-table',
        url: '{:url("poster_template/config_list")}',
        toolbar: '#table-toolbar',
        defaultToolbar: ['filter', 'exports', 'print'],
        cols: [[
            {type: 'checkbox', fixed: 'left'},
            {field: 'id', title: 'ID', width: 200, fixed: 'left'},
            {field: 'config_name', title: '配置名称', width: 200},
            {field: 'template_title', title: '模板标题', width: 200},
            {field: 'template_id', title: '模板ID', width: 120},
            {field: 'status', title: '状态', width: 80, templet: '#status-tpl'},
            {field: 'parameter_stats', title: '参数统计', width: 200, templet: '#param-stats-tpl'},
            {field: 'user_data_count', title: '用户数据', width: 100},
            {field: 'created_at', title: '创建时间', width: 160},
            {title: '操作', width: 300, toolbar: '#row-toolbar', fixed: 'right'}
        ]],
        page: true,
        height: 'full-200'
    });

    // 搜索
    form.on('submit(search)', function(data){
        tableIns.reload({
            where: data.field,
            page: {curr: 1}
        });
        return false;
    });

    // 头工具栏事件
    table.on('toolbar(config-table)', function(obj){
        switch(obj.event){
            case 'refresh':
                tableIns.reload();
                break;
        }
    });

    // 行工具栏事件
    table.on('tool(config-table)', function(obj){
        var data = obj.data;
        
        switch(obj.event){
            case 'edit':
                location.href = '{:url("poster_template/config_edit")}?id=' + data.id;
                break;
                
            case 'copy':
                layer.prompt({
                    title: '复制配置',
                    formType: 0,
                    value: data.config_name + '_副本'
                }, function(value, index){
                    $.post('{:url("poster_template/config_copy")}', {
                        config_id: data.id,
                        new_config_name: value
                    }, function(res){
                        if(res.code == 1){
                            layer.msg('复制成功');
                            tableIns.reload();
                        } else {
                            layer.msg(res.msg);
                        }
                    });
                    layer.close(index);
                });
                break;
                
            case 'preview':
                // 打开预览窗口
                layer.open({
                    type: 2,
                    title: '配置预览',
                    area: ['80%', '80%'],
                    content: '{:url("poster_template/config_preview")}?id=' + data.id
                });
                break;
                
            case 'enable':
            case 'disable':
                var status = obj.event === 'enable' ? 1 : 0;
                var text = obj.event === 'enable' ? '启用' : '禁用';
                
                layer.confirm('确定要' + text + '此配置吗？', function(index){
                    $.post('{:url("poster_template/config_edit")}', {
                        id: data.id,
                        status: status
                    }, function(res){
                        if(res.code == 1){
                            layer.msg(text + '成功');
                            obj.update({status: status});
                        } else {
                            layer.msg(res.msg);
                        }
                    });
                    layer.close(index);
                });
                break;
                
            case 'delete':
                layer.confirm('确定要删除此配置吗？删除后不可恢复！', function(index){
                    $.post('{:url("poster_template/config_delete")}', {
                        id: data.id
                    }, function(res){
                        if(res.code == 1){
                            layer.msg('删除成功');
                            obj.del();
                        } else {
                            layer.msg(res.msg);
                        }
                    });
                    layer.close(index);
                });
                break;
        }
    });
});
</script>

<style>
.param-stats .layui-badge {
    margin-right: 5px;
}
</style>
</div>
