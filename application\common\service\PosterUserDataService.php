<?php
/**
 * 动态参数模板系统 - 用户数据管理服务
 */

namespace app\common\service;

use app\common\model\PosterTemplateConfig;
use app\common\model\PosterUserData;
use app\common\model\PosterGenerationRecord;
use think\facade\Log;

class PosterUserDataService
{
    protected $apiClient;
    
    public function __construct()
    {
        $this->apiClient = new PosterApiClient();
    }
    
    /**
     * 保存用户数据
     * @param array $data
     * @return array
     */
    public function saveUserData($data)
    {
        try {
            // 验证配置是否存在
            $config = PosterTemplateConfig::where('id', $data['config_id'])
                ->where('status', PosterTemplateConfig::STATUS_ENABLED)
                ->find();
            
            if (!$config) {
                throw new \Exception('Template config not found or disabled');
            }
            
            // 验证参数值
            $validation = PosterUserData::validateParameterValues(
                $data['parameter_values'] ?? [],
                $config->parameters
            );
            
            if (!$validation['valid']) {
                throw new \Exception('Parameter validation failed: ' . implode(', ', $validation['errors']));
            }
            
            // 创建用户数据
            $userData = PosterUserData::createUserData($data);
            
            if (!$userData) {
                throw new \Exception('Failed to create user data');
            }
            
            Log::info('User data saved', [
                'data_id' => $userData->id,
                'config_id' => $data['config_id'],
                'user_id' => $data['user_id'] ?? null,
                'is_draft' => $data['is_draft'] ?? true
            ]);
            
            return [
                'success' => true,
                'data' => [
                    'id' => $userData->id,
                    'configId' => $userData->config_id,
                    'templateId' => $config->template_id,
                    'isDraft' => $userData->is_draft,
                    'createdAt' => $userData->created_at
                ]
            ];
            
        } catch (\Exception $e) {
            Log::error('Save user data failed', [
                'data' => $data,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 更新用户数据
     * @param string $dataId
     * @param array $updateData
     * @return array
     */
    public function updateUserData($dataId, $updateData)
    {
        try {
            // 获取现有数据
            $userData = PosterUserData::where('id', $dataId)->with(['config'])->find();
            
            if (!$userData) {
                throw new \Exception('User data not found');
            }
            
            // 如果更新参数值，需要验证
            if (isset($updateData['parameter_values'])) {
                $validation = PosterUserData::validateParameterValues(
                    $updateData['parameter_values'],
                    $userData->config->parameters
                );
                
                if (!$validation['valid']) {
                    throw new \Exception('Parameter validation failed: ' . implode(', ', $validation['errors']));
                }
            }
            
            $result = PosterUserData::updateUserData($dataId, $updateData);
            
            if (!$result) {
                throw new \Exception('Failed to update user data');
            }
            
            Log::info('User data updated', [
                'data_id' => $dataId,
                'updated_fields' => array_keys($updateData)
            ]);
            
            return [
                'success' => true,
                'data' => ['id' => $dataId]
            ];
            
        } catch (\Exception $e) {
            Log::error('Update user data failed', [
                'data_id' => $dataId,
                'update_data' => $updateData,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 生成预览
     * @param string $dataId
     * @return array
     */
    public function generatePreview($dataId)
    {
        try {
            $userData = PosterUserData::where('id', $dataId)->with(['config'])->find();
            
            if (!$userData) {
                throw new \Exception('User data not found');
            }
            
            $response = $this->apiClient->generatePreview(
                $userData->config->template_id,
                $dataId
            );
            
            if (!$response['success']) {
                throw new \Exception($response['error'] ?? 'Failed to generate preview');
            }
            
            // 更新预览URL
            $previewUrl = $response['data']['data']['previewUrl'] ?? '';
            if ($previewUrl) {
                PosterUserData::updateUserData($dataId, ['preview_url' => $previewUrl]);
            }
            
            Log::info('Preview generated', [
                'data_id' => $dataId,
                'preview_url' => $previewUrl
            ]);
            
            return [
                'success' => true,
                'data' => [
                    'previewUrl' => $previewUrl,
                    'dataId' => $dataId
                ]
            ];
            
        } catch (\Exception $e) {
            Log::error('Generate preview failed', [
                'data_id' => $dataId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 生成图片
     * @param string $dataId
     * @param array $options
     * @return array
     */
    public function generateImage($dataId, $options = [])
    {
        try {
            $startTime = microtime(true);
            
            $userData = PosterUserData::where('id', $dataId)->with(['config'])->find();
            
            if (!$userData) {
                throw new \Exception('User data not found');
            }
            
            // 设置默认选项
            $defaultOptions = [
                'width' => 1242,
                'height' => 2208,
                'quality' => 0.9,
                'type' => 'file'
            ];
            $options = array_merge($defaultOptions, $options);
            $options['parameterDataId'] = $dataId;
            
            $response = $this->apiClient->generateImage($options);
            
            if (!$response['success']) {
                // 记录失败
                PosterGenerationRecord::recordFailure(
                    $dataId,
                    $response['error'] ?? 'Unknown error',
                    $options,
                    microtime(true) - $startTime
                );
                
                throw new \Exception($response['error'] ?? 'Failed to generate image');
            }
            
            $imageUrl = $response['data']['data']['url'] ?? '';
            $fileSize = $response['data']['data']['fileSize'] ?? 0;
            $generationTime = microtime(true) - $startTime;
            
            // 记录成功
            PosterGenerationRecord::recordSuccess(
                $dataId,
                $imageUrl,
                $options,
                $generationTime,
                $fileSize
            );
            
            // 更新用户数据
            PosterUserData::updateUserData($dataId, ['generated_image_url' => $imageUrl]);
            
            Log::info('Image generated', [
                'data_id' => $dataId,
                'image_url' => $imageUrl,
                'generation_time' => $generationTime
            ]);
            
            return [
                'success' => true,
                'data' => [
                    'imageUrl' => $imageUrl,
                    'fileSize' => $fileSize,
                    'generationTime' => $generationTime,
                    'options' => $options
                ]
            ];
            
        } catch (\Exception $e) {
            Log::error('Generate image failed', [
                'data_id' => $dataId,
                'options' => $options,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 获取用户数据列表
     * @param array $filters
     * @param int $page
     * @param int $pageSize
     * @return array
     */
    public function getUserDataList($filters = [], $page = 1, $pageSize = 15)
    {
        try {
            $where = [];
            
            // 处理筛选条件
            if (!empty($filters['config_id'])) {
                $where['config_id'] = $filters['config_id'];
            }
            if (!empty($filters['user_id'])) {
                $where['user_id'] = $filters['user_id'];
            }
            if (!empty($filters['session_id'])) {
                $where['session_id'] = $filters['session_id'];
            }
            if (isset($filters['is_draft'])) {
                $where['is_draft'] = $filters['is_draft'];
            }
            
            // 分页查询
            $query = PosterUserData::where($where)->with(['config']);
            $total = $query->count();
            $list = $query->order('updated_at desc')
                         ->page($page, $pageSize)
                         ->select();
            
            return [
                'success' => true,
                'data' => [
                    'list' => $list,
                    'total' => $total,
                    'page' => $page,
                    'pageSize' => $pageSize,
                    'totalPages' => ceil($total / $pageSize)
                ]
            ];
            
        } catch (\Exception $e) {
            Log::error('Get user data list failed', [
                'filters' => $filters,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
