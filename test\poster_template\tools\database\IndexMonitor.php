<?php
/**
 * 动态参数模板系统数据库索引监控工具
 */

namespace test\poster_template\tools\database;

use think\facade\Db;

class IndexMonitor
{
    /**
     * 检查索引使用情况
     */
    public static function checkIndexUsage()
    {
        echo "检查动态参数模板系统索引使用情况...\n\n";
        
        $tables = [
            'ls_poster_template_configs',
            'ls_poster_user_data', 
            'ls_poster_generation_records'
        ];
        
        foreach ($tables as $table) {
            self::analyzeTableIndexes($table);
        }
    }
    
    /**
     * 分析单个表的索引
     */
    protected static function analyzeTableIndexes($tableName)
    {
        echo "=== {$tableName} 索引分析 ===\n";
        
        // 获取表的索引信息
        $indexes = Db::query("SHOW INDEX FROM {$tableName}");
        
        echo "索引列表:\n";
        $indexGroups = [];
        foreach ($indexes as $index) {
            $indexName = $index['Key_name'];
            if (!isset($indexGroups[$indexName])) {
                $indexGroups[$indexName] = [
                    'name' => $indexName,
                    'unique' => $index['Non_unique'] == 0,
                    'columns' => [],
                    'cardinality' => 0
                ];
            }
            $indexGroups[$indexName]['columns'][] = $index['Column_name'];
            $indexGroups[$indexName]['cardinality'] += $index['Cardinality'];
        }
        
        foreach ($indexGroups as $index) {
            $type = $index['unique'] ? 'UNIQUE' : 'INDEX';
            $columns = implode(', ', $index['columns']);
            echo "  - {$index['name']} ({$type}): {$columns} [基数: {$index['cardinality']}]\n";
        }
        
        // 获取表统计信息
        $stats = Db::query("SHOW TABLE STATUS LIKE '{$tableName}'");
        if (!empty($stats)) {
            $stat = $stats[0];
            echo "表统计信息:\n";
            echo "  - 行数: " . number_format($stat['Rows']) . "\n";
            echo "  - 数据大小: " . self::formatBytes($stat['Data_length']) . "\n";
            echo "  - 索引大小: " . self::formatBytes($stat['Index_length']) . "\n";
            echo "  - 平均行长度: " . number_format($stat['Avg_row_length']) . " bytes\n";
        }
        
        echo "\n";
    }
    
    /**
     * 检查慢查询和索引建议
     */
    public static function checkSlowQueries()
    {
        echo "检查可能的慢查询和索引建议...\n\n";
        
        // 常见查询模式检查
        $queryPatterns = [
            '模板配置查询' => [
                'table' => 'ls_poster_template_configs',
                'queries' => [
                    "SELECT * FROM ls_poster_template_configs WHERE template_id = '1' AND status = 1",
                    "SELECT * FROM ls_poster_template_configs WHERE created_by = 'admin' AND status = 1",
                    "SELECT * FROM ls_poster_template_configs WHERE status = 1 ORDER BY created_at DESC LIMIT 10"
                ]
            ],
            '用户数据查询' => [
                'table' => 'ls_poster_user_data',
                'queries' => [
                    "SELECT * FROM ls_poster_user_data WHERE user_id = 'user_001' AND is_draft = 0",
                    "SELECT * FROM ls_poster_user_data WHERE config_id = 'config_001' ORDER BY created_at DESC",
                    "SELECT * FROM ls_poster_user_data WHERE session_id = 'session_001' AND is_draft = 1"
                ]
            ],
            '生成记录查询' => [
                'table' => 'ls_poster_generation_records',
                'queries' => [
                    "SELECT * FROM ls_poster_generation_records WHERE data_id = 'data_001' ORDER BY created_at DESC",
                    "SELECT * FROM ls_poster_generation_records WHERE status = 1 AND created_at >= '2025-01-01'",
                    "SELECT AVG(generation_time) FROM ls_poster_generation_records WHERE status = 1"
                ]
            ]
        ];
        
        foreach ($queryPatterns as $category => $info) {
            echo "=== {$category} ===\n";
            foreach ($info['queries'] as $query) {
                self::explainQuery($query);
            }
            echo "\n";
        }
    }
    
    /**
     * 分析查询执行计划
     */
    protected static function explainQuery($query)
    {
        try {
            $explain = Db::query("EXPLAIN {$query}");
            if (!empty($explain)) {
                $result = $explain[0];
                echo "查询: " . substr($query, 0, 60) . "...\n";
                echo "  - 类型: {$result['type']}\n";
                echo "  - 可能的键: " . ($result['possible_keys'] ?: 'NULL') . "\n";
                echo "  - 使用的键: " . ($result['key'] ?: 'NULL') . "\n";
                echo "  - 扫描行数: " . number_format($result['rows']) . "\n";
                
                // 性能建议
                if ($result['type'] === 'ALL') {
                    echo "  ⚠️  警告: 全表扫描，建议添加索引\n";
                } elseif ($result['rows'] > 1000) {
                    echo "  ⚠️  注意: 扫描行数较多，可能需要优化\n";
                } else {
                    echo "  ✅ 查询性能良好\n";
                }
                echo "\n";
            }
        } catch (\Exception $e) {
            echo "查询分析失败: {$e->getMessage()}\n\n";
        }
    }
    
    /**
     * 生成索引优化建议
     */
    public static function generateIndexRecommendations()
    {
        echo "生成索引优化建议...\n\n";
        
        $recommendations = [
            '模板配置表优化建议' => [
                '如果经常按模板ID和状态查询，确保 idx_template_status 索引存在',
                '如果需要按创建者和状态筛选，确保 idx_created_by_status 索引存在',
                '考虑为 template_title 添加全文索引以支持搜索功能',
                '如果参数数量是常用筛选条件，使用 parameter_count 虚拟列索引'
            ],
            '用户数据表优化建议' => [
                '确保用户ID和草稿状态的复合索引 idx_user_draft 存在',
                '为会话ID和草稿状态添加复合索引 idx_session_draft',
                '考虑为配置ID和创建时间添加复合索引以优化排序查询',
                '如果需要按更新时间范围查询，添加 updated_at 相关索引'
            ],
            '生成记录表优化建议' => [
                '确保数据ID和状态的复合索引 idx_data_status 存在',
                '为状态和创建时间添加复合索引以优化统计查询',
                '考虑为图片尺寸添加复合索引以支持尺寸统计',
                '使用 size_mb 虚拟列索引优化文件大小相关查询',
                '考虑按月分区以提高大数据量下的查询性能'
            ]
        ];
        
        foreach ($recommendations as $category => $items) {
            echo "=== {$category} ===\n";
            foreach ($items as $item) {
                echo "  • {$item}\n";
            }
            echo "\n";
        }
    }
    
    /**
     * 执行索引维护
     */
    public static function maintainIndexes()
    {
        echo "执行索引维护...\n\n";
        
        $tables = [
            'ls_poster_template_configs',
            'ls_poster_user_data',
            'ls_poster_generation_records'
        ];
        
        foreach ($tables as $table) {
            echo "维护表: {$table}\n";
            
            try {
                // 分析表
                Db::execute("ANALYZE TABLE {$table}");
                echo "  ✅ 表分析完成\n";
                
                // 优化表
                Db::execute("OPTIMIZE TABLE {$table}");
                echo "  ✅ 表优化完成\n";
                
            } catch (\Exception $e) {
                echo "  ❌ 维护失败: {$e->getMessage()}\n";
            }
            
            echo "\n";
        }
    }
    
    /**
     * 格式化字节数
     */
    protected static function formatBytes($bytes)
    {
        if ($bytes < 1024) {
            return $bytes . ' B';
        } elseif ($bytes < 1048576) {
            return round($bytes / 1024, 2) . ' KB';
        } elseif ($bytes < 1073741824) {
            return round($bytes / 1048576, 2) . ' MB';
        } else {
            return round($bytes / 1073741824, 2) . ' GB';
        }
    }
    
    /**
     * 运行完整的索引检查
     */
    public static function runFullCheck()
    {
        echo "动态参数模板系统数据库索引完整检查\n";
        echo str_repeat('=', 50) . "\n\n";
        
        self::checkIndexUsage();
        self::checkSlowQueries();
        self::generateIndexRecommendations();
        self::maintainIndexes();
        
        echo "索引检查完成！\n";
    }
}
