<?php
/**
 * 动态参数模板系统API认证中间件
 */

namespace app\api\middleware;

use think\facade\Config;
use think\facade\Log;
use think\Response;

class PosterApiAuth
{
    /**
     * 处理请求
     * @param \think\Request $request
     * @param \Closure $next
     * @return Response
     */
    public function handle($request, \Closure $next)
    {
        // 获取配置的API Key
        $configApiKey = Config::get('poster_template.external_api.api_key');
        
        if (empty($configApiKey) || $configApiKey === 'your-secure-api-key-here') {
            Log::error('Poster API Key not configured properly');
            return $this->errorResponse('API Key not configured', 500);
        }
        
        // 获取请求头中的API Key
        $authHeader = $request->header('Authorization');
        if (empty($authHeader)) {
            Log::warning('Poster API request without Authorization header', [
                'ip' => $request->ip(),
                'url' => $request->url(),
                'method' => $request->method()
            ]);
            return $this->errorResponse('Missing Authorization header', 401);
        }
        
        // 解析Bearer Token
        $prefix = Config::get('poster_template.security.api_key_prefix', 'Bearer ');
        if (!str_starts_with($authHeader, $prefix)) {
            Log::warning('Poster API invalid Authorization header format', [
                'header' => $authHeader,
                'ip' => $request->ip()
            ]);
            return $this->errorResponse('Invalid Authorization header format', 401);
        }
        
        $apiKey = substr($authHeader, strlen($prefix));
        
        // 验证API Key
        if ($apiKey !== $configApiKey) {
            Log::warning('Poster API invalid API key', [
                'provided_key' => substr($apiKey, 0, 8) . '***',
                'ip' => $request->ip(),
                'url' => $request->url()
            ]);
            return $this->errorResponse('Invalid API key', 401);
        }
        
        // 记录API调用日志
        Log::info('Poster API authenticated request', [
            'ip' => $request->ip(),
            'url' => $request->url(),
            'method' => $request->method(),
            'user_agent' => $request->header('User-Agent')
        ]);
        
        // 添加认证标识到请求中
        $request->withAttribute('poster_api_authenticated', true);
        
        return $next($request);
    }
    
    /**
     * 返回错误响应
     * @param string $message
     * @param int $code
     * @return Response
     */
    protected function errorResponse($message, $code = 400)
    {
        $data = [
            'code' => $code,
            'message' => $message,
            'error' => [
                'reason' => $message,
                'timestamp' => date('c')
            ]
        ];
        
        return Response::create($data, 'json', $code)
            ->header('Content-Type', 'application/json; charset=utf-8');
    }
}
