<?php
/**
 * 动态参数模板系统 - 模板服务单元测试
 */

namespace test\poster_template\unit;

use PHPUnit\Framework\TestCase;
use app\common\service\PosterTemplateService;
use app\common\model\PosterTemplateConfig;

class PosterTemplateServiceTest extends TestCase
{
    protected $templateService;
    protected $testConfigId;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->templateService = new PosterTemplateService();
        
        // 创建测试配置
        $this->createTestConfig();
    }
    
    protected function tearDown(): void
    {
        // 清理测试数据
        if ($this->testConfigId) {
            PosterTemplateConfig::where('id', $this->testConfigId)->delete();
        }
        
        parent::tearDown();
    }
    
    /**
     * 创建测试配置
     */
    private function createTestConfig()
    {
        $configData = [
            'id' => 'test_config_' . uniqid(),
            'template_id' => 'test_template_001',
            'template_title' => '测试模板',
            'config_name' => '单元测试配置',
            'config_description' => '用于单元测试的配置',
            'parameters' => [
                [
                    'id' => 'param_001',
                    'elementUuid' => 'uuid_001',
                    'parameterName' => 'test_text',
                    'parameterLabel' => '测试文本',
                    'parameterType' => 'text',
                    'isRequired' => true,
                    'isEnabled' => true,
                    'defaultValue' => '默认值',
                    'validationRules' => ['maxLength' => 100],
                    'displayOrder' => 1
                ]
            ],
            'created_by' => 'unit_test',
            'status' => 1
        ];
        
        $config = PosterTemplateConfig::create($configData);
        $this->testConfigId = $config->id;
    }
    
    /**
     * 测试获取配置列表
     */
    public function testGetConfigList()
    {
        $result = $this->templateService->getConfigList(['status' => 1], 1, 10);
        
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('list', $result['data']);
        $this->assertArrayHasKey('total', $result['data']);
        $this->assertIsArray($result['data']['list']);
    }
    
    /**
     * 测试获取配置详情
     */
    public function testGetConfigDetail()
    {
        $result = $this->templateService->getConfigDetail($this->testConfigId);
        
        $this->assertTrue($result['success']);
        $this->assertEquals($this->testConfigId, $result['data']['id']);
        $this->assertEquals('单元测试配置', $result['data']['config_name']);
        $this->assertArrayHasKey('parameter_stats', $result['data']);
    }
    
    /**
     * 测试获取不存在的配置
     */
    public function testGetNonExistentConfig()
    {
        $result = $this->templateService->getConfigDetail('non_existent_id');
        
        $this->assertFalse($result['success']);
        $this->assertStringContains('not found', $result['error']);
    }
    
    /**
     * 测试更新配置
     */
    public function testUpdateConfig()
    {
        $updateData = [
            'config_name' => '更新后的配置名称',
            'config_description' => '更新后的描述'
        ];
        
        $result = $this->templateService->updateConfig($this->testConfigId, $updateData);
        
        $this->assertTrue($result['success']);
        
        // 验证更新结果
        $updatedConfig = PosterTemplateConfig::where('id', $this->testConfigId)->find();
        $this->assertEquals('更新后的配置名称', $updatedConfig->config_name);
        $this->assertEquals('更新后的描述', $updatedConfig->config_description);
    }
    
    /**
     * 测试复制配置
     */
    public function testCopyConfig()
    {
        $newConfigName = '复制的配置_' . uniqid();
        
        $result = $this->templateService->copyConfig($this->testConfigId, $newConfigName, 'unit_test');
        
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('configId', $result['data']);
        
        // 验证复制结果
        $copiedConfig = PosterTemplateConfig::where('id', $result['data']['configId'])->find();
        $this->assertEquals($newConfigName, $copiedConfig->config_name);
        $this->assertEquals('unit_test', $copiedConfig->created_by);
        
        // 清理复制的配置
        $copiedConfig->delete();
    }
    
    /**
     * 测试参数验证
     */
    public function testParameterValidation()
    {
        // 测试有效参数
        $validParameters = [
            [
                'id' => 'param_001',
                'elementUuid' => 'uuid_001',
                'parameterName' => 'test_param',
                'parameterType' => 'text'
            ]
        ];
        
        $this->assertTrue(PosterTemplateConfig::validateParameters($validParameters));
        
        // 测试无效参数
        $invalidParameters = [
            [
                'id' => 'param_001',
                // 缺少必需字段
            ]
        ];
        
        $this->assertFalse(PosterTemplateConfig::validateParameters($invalidParameters));
    }
    
    /**
     * 测试配置统计信息
     */
    public function testConfigStats()
    {
        $config = PosterTemplateConfig::where('id', $this->testConfigId)->find();
        $stats = $config->getParameterStats();
        
        $this->assertArrayHasKey('total', $stats);
        $this->assertArrayHasKey('enabled', $stats);
        $this->assertArrayHasKey('required', $stats);
        $this->assertArrayHasKey('types', $stats);
        
        $this->assertEquals(1, $stats['total']);
        $this->assertEquals(1, $stats['enabled']);
        $this->assertEquals(1, $stats['required']);
        $this->assertArrayHasKey('text', $stats['types']);
    }
    
    /**
     * 测试配置状态管理
     */
    public function testConfigStatusManagement()
    {
        // 测试禁用配置
        $result = $this->templateService->updateConfig($this->testConfigId, ['status' => 0]);
        $this->assertTrue($result['success']);
        
        $config = PosterTemplateConfig::where('id', $this->testConfigId)->find();
        $this->assertEquals(0, $config->status);
        
        // 测试启用配置
        $result = $this->templateService->updateConfig($this->testConfigId, ['status' => 1]);
        $this->assertTrue($result['success']);
        
        $config = PosterTemplateConfig::where('id', $this->testConfigId)->find();
        $this->assertEquals(1, $config->status);
    }
    
    /**
     * 测试错误处理
     */
    public function testErrorHandling()
    {
        // 测试更新不存在的配置
        $result = $this->templateService->updateConfig('non_existent_id', ['config_name' => 'test']);
        $this->assertFalse($result['success']);
        
        // 测试复制不存在的配置
        $result = $this->templateService->copyConfig('non_existent_id', 'test', 'test');
        $this->assertFalse($result['success']);
        
        // 测试删除不存在的配置
        $result = $this->templateService->deleteConfig('non_existent_id');
        $this->assertFalse($result['success']);
    }
    
    /**
     * 测试配置搜索功能
     */
    public function testConfigSearch()
    {
        // 按关键词搜索
        $result = $this->templateService->getConfigList(['keyword' => '单元测试'], 1, 10);
        $this->assertTrue($result['success']);
        
        $found = false;
        foreach ($result['data']['list'] as $config) {
            if ($config['id'] === $this->testConfigId) {
                $found = true;
                break;
            }
        }
        $this->assertTrue($found, '应该能找到测试配置');
        
        // 按不存在的关键词搜索
        $result = $this->templateService->getConfigList(['keyword' => 'non_existent_keyword'], 1, 10);
        $this->assertTrue($result['success']);
        $this->assertEquals(0, $result['data']['total']);
    }
}
