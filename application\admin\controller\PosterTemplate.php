<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------

/**
 * 动态参数模板系统 - 后台管理控制器
 */

namespace app\admin\controller;

use app\common\service\PosterTemplateService;
use app\common\service\PosterUserDataService;

class PosterTemplate extends AdminBase
{
    protected $templateService;
    protected $userDataService;
    
    public function __construct()
    {
        parent::__construct();
        $this->templateService = new PosterTemplateService();
        $this->userDataService = new PosterUserDataService();
    }
    
    /**
     * 模板配置列表页面（下划线命名）
     */
    public function config_list()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();

            $filters = [
                'template_id' => $get['template_id'] ?? '',
                'created_by' => $get['created_by'] ?? '',
                'status' => $get['status'] ?? '',
                'keyword' => $get['keyword'] ?? ''
            ];

            $page = $get['page'] ?? 1;
            $pageSize = $get['limit'] ?? $this->page_size;

            $result = $this->templateService->getConfigList($filters, $page, $pageSize);

            if ($result['success']) {
                $this->_success('', $result['data']);
            } else {
                $this->_error($result['error']);
            }
        }

        return $this->fetch();
    }

    /**
     * 模板配置列表页面（驼峰命名 - 备用）
     */
    public function configList()
    {
        return $this->config_list();
    }

    /**
     * 添加模板配置页面（下划线命名）
     */
    public function config_add()
    {
        return $this->configAdd();
    }

    /**
     * 编辑模板配置页面（下划线命名）
     */
    public function config_edit($id = '')
    {
        return $this->configEdit($id);
    }

    /**
     * 删除模板配置（下划线命名）
     */
    public function config_delete($id = '')
    {
        return $this->configDelete($id);
    }
    
    /**
     * 添加模板配置页面
     */
    public function configAdd()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            
            $result = $this->validate($post, [
                'template_id' => 'require',
                'config_name' => 'require|max:255',
                'config_description' => 'max:1000'
            ]);
            
            if ($result !== true) {
                $this->_error($result);
            }
            
            $result = $this->templateService->parseAndCreateConfig(
                $post['template_id'],
                $post['config_name'],
                $post['config_description'] ?? '',
                $this->admin_id
            );
            
            if ($result['success']) {
                $this->_success('配置创建成功！', $result['data']);
            } else {
                $this->_error($result['error']);
            }
        }
        
        return $this->fetch();
    }
    
    /**
     * 编辑模板配置页面
     */
    public function configEdit($id)
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            
            $result = $this->validate($post, [
                'config_name' => 'require|max:255',
                'config_description' => 'max:1000',
                'parameters' => 'require|array'
            ]);
            
            if ($result !== true) {
                $this->_error($result);
            }
            
            $updateData = [
                'config_name' => $post['config_name'],
                'config_description' => $post['config_description'] ?? '',
                'parameters' => $post['parameters']
            ];
            
            $result = $this->templateService->updateConfig($id, $updateData);
            
            if ($result['success']) {
                $this->_success('配置更新成功！');
            } else {
                $this->_error($result['error']);
            }
        }
        
        // 获取配置详情
        $result = $this->templateService->getConfigDetail($id);
        if (!$result['success']) {
            $this->error($result['error']);
        }
        
        $this->assign('config', $result['data']);
        return $this->fetch();
    }
    
    /**
     * 删除模板配置
     */
    public function configDelete($id)
    {
        $result = $this->templateService->deleteConfig($id);
        
        if ($result['success']) {
            $this->_success('配置删除成功！');
        } else {
            $this->_error($result['error']);
        }
    }
    
    /**
     * 复制模板配置
     */
    public function configCopy()
    {
        $post = $this->request->post();
        
        $result = $this->validate($post, [
            'config_id' => 'require',
            'new_config_name' => 'require|max:255'
        ]);
        
        if ($result !== true) {
            $this->_error($result);
        }
        
        $result = $this->templateService->copyConfig(
            $post['config_id'],
            $post['new_config_name'],
            $this->admin_id
        );
        
        if ($result['success']) {
            $this->_success('配置复制成功！', $result['data']);
        } else {
            $this->_error($result['error']);
        }
    }
    
    /**
     * 获取模板列表（AJAX）
     */
    public function getTemplates()
    {
        $get = $this->request->get();
        
        $params = [
            'page' => $get['page'] ?? 1,
            'pageSize' => $get['pageSize'] ?? 12,
            'category' => $get['category'] ?? '',
            'keyword' => $get['keyword'] ?? ''
        ];
        
        $result = $this->templateService->getTemplateList($params);
        
        if ($result['success']) {
            $this->_success('', $result['data']);
        } else {
            $this->_error($result['error']);
        }
    }
    
    /**
     * 解析模板（AJAX）
     */
    public function parseTemplate()
    {
        $post = $this->request->post();
        
        if (empty($post['template_id'])) {
            $this->_error('模板ID不能为空');
        }
        
        $result = $this->templateService->parseAndCreateConfig(
            $post['template_id'],
            '临时配置_' . date('YmdHis'),
            '临时解析配置',
            $this->admin_id
        );
        
        if ($result['success']) {
            $this->_success('模板解析成功！', $result['data']);
        } else {
            $this->_error($result['error']);
        }
    }
    
    /**
     * 用户数据列表页面
     */
    public function userDataList()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            
            $filters = [
                'config_id' => $get['config_id'] ?? '',
                'user_id' => $get['user_id'] ?? '',
                'is_draft' => isset($get['is_draft']) ? (bool)$get['is_draft'] : null
            ];
            
            $page = $get['page'] ?? 1;
            $pageSize = $get['limit'] ?? $this->page_size;
            
            $result = $this->userDataService->getUserDataList($filters, $page, $pageSize);
            
            if ($result['success']) {
                $this->_success('', $result['data']);
            } else {
                $this->_error($result['error']);
            }
        }
        
        return $this->fetch();
    }
    
    /**
     * 生成预览（AJAX）
     */
    public function generatePreview()
    {
        $post = $this->request->post();
        
        if (empty($post['data_id'])) {
            $this->_error('数据ID不能为空');
        }
        
        $result = $this->userDataService->generatePreview($post['data_id']);
        
        if ($result['success']) {
            $this->_success('预览生成成功！', $result['data']);
        } else {
            $this->_error($result['error']);
        }
    }
    
    /**
     * 生成图片（AJAX）
     */
    public function generateImage()
    {
        $post = $this->request->post();
        
        if (empty($post['data_id'])) {
            $this->_error('数据ID不能为空');
        }
        
        $options = [
            'width' => $post['width'] ?? 1242,
            'height' => $post['height'] ?? 2208,
            'quality' => $post['quality'] ?? 0.9,
            'type' => $post['type'] ?? 'file'
        ];
        
        $result = $this->userDataService->generateImage($post['data_id'], $options);
        
        if ($result['success']) {
            $this->_success('图片生成成功！', $result['data']);
        } else {
            $this->_error($result['error']);
        }
    }
    
    /**
     * 统计数据（AJAX）
     */
    public function getStats()
    {
        try {
            $stats = [
                'total_configs' => \app\common\model\PosterTemplateConfig::where('status', 1)->count(),
                'total_user_data' => \app\common\model\PosterUserData::count(),
                'total_generations' => \app\common\model\PosterGenerationRecord::count(),
                'success_rate' => 0
            ];
            
            $generationStats = \app\common\model\PosterGenerationRecord::getGenerationStats();
            $stats['success_rate'] = $generationStats['success_rate'];
            $stats['avg_generation_time'] = $generationStats['avg_generation_time'];
            
            $this->_success('', $stats);
            
        } catch (\Exception $e) {
            $this->_error('获取统计数据失败：' . $e->getMessage());
        }
    }
}
