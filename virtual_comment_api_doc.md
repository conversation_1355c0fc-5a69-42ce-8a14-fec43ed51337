# 虚拟评价API接口文档

## 添加虚拟评价

### 接口说明
该接口用于允许"评论家"等级的会员添加虚拟商品评价。

### 请求URL
```
POST /api/goods_comment/addVirtualComment
```

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
| --- | --- | --- | --- |
| goods_id | int | 是 | 商品ID |
| avatar | string | 是 | 评价用户头像URL |
| nickname | string | 是 | 评价用户昵称 |
| level | int | 是 | 评价用户等级ID |
| comment_time | string | 是 | 评价时间，格式：YYYY-MM-DD HH:MM:SS |
| score | int | 是 | 评价等级，范围：1-5 |
| comment | string | 是 | 评价内容 |
| comment_image | array | 否 | 评价图片URL数组 |

### 请求示例
```json
{
    "goods_id": 123,
    "avatar": "/uploads/user/avatar/20230612/123456.jpg",
    "nickname": "测试用户",
    "level": 1,
    "comment_time": "2023-06-12 10:00:00",
    "score": 5,
    "comment": "商品非常好，质量很棒，物流很快，服务态度也很好！",
    "comment_image": [
        "/uploads/comment/image/20230612/1.jpg",
        "/uploads/comment/image/20230612/2.jpg"
    ]
}
```

### 返回参数

| 参数名 | 类型 | 说明 |
| --- | --- | --- |
| code | int | 状态码，1=成功，0=失败 |
| msg | string | 提示信息 |
| data | object | 返回数据，成功时为空对象 |

### 成功返回示例
```json
{
    "code": 1,
    "msg": "评论添加成功",
    "data": {}
}
```

### 失败返回示例
```json
{
    "code": 0,
    "msg": "您不是评论家，无法添加虚拟评价",
    "data": {}
}
```

### 错误码说明

| 错误信息 | 说明 |
| --- | --- |
| 您不是评论家，无法添加虚拟评价 | 当前用户不是"评论家"等级，无法添加虚拟评价 |
| 您今日添加虚拟评价的数量已达上限 | 用户当日添加的虚拟评价数量已达到限制（99条） |
| 商品不存在或已下架 | 指定的商品ID无效或商品已下架 |
| 当前商品规格参数错误 | 商品规格信息有误 |
| 请选择会员头像 | 头像参数缺失 |
| 请填写昵称 | 昵称参数缺失 |
| 请选择会员等级 | 等级参数缺失 |
| 请选择评价时间 | 评价时间参数缺失 |
| 评价时间不能超过当前时间 | 评价时间设置有误 |
| 请选择评价等级 | 评价等级参数缺失 |
| 评价等级必须在1-5之间 | 评价等级设置有误 |
| 请填写评价内容 | 评价内容参数缺失 |

## 权限说明

1. 只有"评论家"等级的会员才能添加虚拟评价
2. 每个"评论家"每天可添加的虚拟评价数量上限为99条
3. 虚拟评价会在前端与真实评价一起展示，但在后台可以通过 `created_by_user_id` 字段区分 