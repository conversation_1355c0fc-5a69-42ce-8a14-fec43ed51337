<?php
/**
 * 动态参数模板系统 - 用户数据模型
 */

namespace app\common\model;

use think\Model;

class PosterUserData extends Model
{
    // 表名
    protected $table = 'ls_poster_user_data';
    
    // 主键
    protected $pk = 'id';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    // JSON字段
    protected $json = ['parameter_values'];
    protected $jsonAssoc = true;
    
    // 类型转换
    protected $type = [
        'is_draft' => 'boolean',
    ];
    
    /**
     * 参数值获取器 - 确保返回数组
     * @param $value
     * @return array
     */
    public function getParameterValuesAttr($value)
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return $decoded ?: [];
        }
        return $value ?: [];
    }
    
    /**
     * 参数值设置器 - 确保存储为JSON
     * @param $value
     * @return string
     */
    public function setParameterValuesAttr($value)
    {
        return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }
    
    /**
     * 草稿状态获取器
     * @param $value
     * @return string
     */
    public function getIsDraftTextAttr($value, $data)
    {
        return $data['is_draft'] ? '草稿' : '正式';
    }
    
    /**
     * 关联模板配置
     * @return \think\model\relation\BelongsTo
     */
    public function config()
    {
        return $this->belongsTo(PosterTemplateConfig::class, 'config_id', 'id');
    }
    
    /**
     * 关联生成记录
     * @return \think\model\relation\HasMany
     */
    public function generationRecords()
    {
        return $this->hasMany(PosterGenerationRecord::class, 'data_id', 'id');
    }
    
    /**
     * 关联用户信息（如果有用户表）
     * @return \think\model\relation\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
    
    /**
     * 创建用户数据
     * @param array $data
     * @return PosterUserData|false
     */
    public static function createUserData($data)
    {
        // 生成唯一ID
        if (empty($data['id'])) {
            $data['id'] = self::generateId();
        }
        
        // 验证必填字段
        if (empty($data['config_id']) || empty($data['parameter_values'])) {
            return false;
        }
        
        // 确保用户ID或会话ID至少有一个
        if (empty($data['user_id']) && empty($data['session_id'])) {
            return false;
        }
        
        return self::create($data);
    }
    
    /**
     * 更新用户数据
     * @param string $id
     * @param array $data
     * @return bool
     */
    public static function updateUserData($id, $data)
    {
        return self::where('id', $id)->update($data) !== false;
    }
    
    /**
     * 获取用户的数据列表
     * @param string $userId
     * @param bool $includeDraft
     * @return \think\Collection
     */
    public static function getUserDataList($userId, $includeDraft = true)
    {
        $where = ['user_id' => $userId];
        if (!$includeDraft) {
            $where['is_draft'] = false;
        }
        
        return self::where($where)
            ->with(['config'])
            ->order('updated_at desc')
            ->select();
    }
    
    /**
     * 获取会话的数据列表
     * @param string $sessionId
     * @param bool $includeDraft
     * @return \think\Collection
     */
    public static function getSessionDataList($sessionId, $includeDraft = true)
    {
        $where = ['session_id' => $sessionId];
        if (!$includeDraft) {
            $where['is_draft'] = false;
        }
        
        return self::where($where)
            ->with(['config'])
            ->order('updated_at desc')
            ->select();
    }
    
    /**
     * 获取配置的数据列表
     * @param string $configId
     * @param array $filters
     * @return \think\Collection
     */
    public static function getConfigDataList($configId, $filters = [])
    {
        $where = ['config_id' => $configId];
        $where = array_merge($where, $filters);
        
        return self::where($where)
            ->order('created_at desc')
            ->select();
    }
    
    /**
     * 保存草稿
     * @param array $data
     * @return PosterUserData|false
     */
    public static function saveDraft($data)
    {
        $data['is_draft'] = true;
        return self::createUserData($data);
    }
    
    /**
     * 发布草稿（转为正式数据）
     * @param string $id
     * @return bool
     */
    public static function publishDraft($id)
    {
        return self::where('id', $id)->update(['is_draft' => false]) !== false;
    }
    
    /**
     * 删除数据
     * @param string $id
     * @return bool
     */
    public static function deleteData($id)
    {
        return self::where('id', $id)->delete() !== false;
    }
    
    /**
     * 生成唯一ID
     * @return string
     */
    public static function generateId()
    {
        return 'data_' . date('YmdHis') . '_' . uniqid();
    }
    
    /**
     * 验证参数值
     * @param array $parameterValues
     * @param array $parameterDefinitions
     * @return array 验证结果 ['valid' => bool, 'errors' => array]
     */
    public static function validateParameterValues($parameterValues, $parameterDefinitions)
    {
        $errors = [];
        
        foreach ($parameterDefinitions as $param) {
            $name = $param['parameterName'];
            $value = $parameterValues[$name] ?? '';
            
            // 检查必填项
            if (!empty($param['isRequired']) && empty($value)) {
                $errors[] = "参数 {$param['parameterLabel']} 为必填项";
                continue;
            }
            
            // 检查长度限制
            if (!empty($param['validationRules']['maxLength'])) {
                $maxLength = $param['validationRules']['maxLength'];
                if (mb_strlen($value) > $maxLength) {
                    $errors[] = "参数 {$param['parameterLabel']} 长度不能超过 {$maxLength} 个字符";
                }
            }
            
            // 检查类型特定的验证
            switch ($param['parameterType']) {
                case 'email':
                    if (!empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                        $errors[] = "参数 {$param['parameterLabel']} 必须是有效的邮箱地址";
                    }
                    break;
                case 'phone':
                    if (!empty($value) && !preg_match('/^1[3-9]\d{9}$/', $value)) {
                        $errors[] = "参数 {$param['parameterLabel']} 必须是有效的手机号码";
                    }
                    break;
            }
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * 获取数据统计
     * @param string $configId
     * @return array
     */
    public static function getDataStats($configId = null)
    {
        $where = [];
        if ($configId) {
            $where['config_id'] = $configId;
        }
        
        $total = self::where($where)->count();
        $drafts = self::where($where)->where('is_draft', true)->count();
        $published = self::where($where)->where('is_draft', false)->count();
        
        return [
            'total' => $total,
            'drafts' => $drafts,
            'published' => $published,
            'draft_rate' => $total > 0 ? round($drafts / $total * 100, 2) : 0
        ];
    }
}
