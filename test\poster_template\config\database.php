<?php
/**
 * 动态参数模板系统测试数据库配置
 */

return [
    // 测试数据库配置
    'test' => [
        'type'            => 'mysql',
        'hostname'        => env('TEST_DB_HOST', '127.0.0.1'),
        'database'        => env('TEST_DB_NAME', 'likeshop_test'),
        'username'        => env('TEST_DB_USER', 'root'),
        'password'        => env('TEST_DB_PASS', 'root'),
        'hostport'        => env('TEST_DB_PORT', '3306'),
        'charset'         => 'utf8mb4',
        'prefix'          => 'ls_',
        'debug'           => true,
        'deploy'          => 0,
        'rw_separate'     => false,
        'master_num'      => 1,
        'slave_no'        => '',
        'read_master'     => false,
        'fields_strict'   => true,
        'resultset_type'  => 'array',
        'auto_timestamp'  => false,
        'datetime_format' => 'Y-m-d H:i:s',
        'sql_explain'     => false,
        'builder'         => '',
        'query'           => '\\think\\db\\Query',
        'break_reconnect' => false,
        'break_match_str' => [],
    ],
    
    // 内存数据库配置（用于快速测试）
    'memory' => [
        'type'            => 'sqlite',
        'database'        => ':memory:',
        'prefix'          => 'ls_',
        'charset'         => 'utf8',
        'debug'           => true,
        'fields_strict'   => false,
        'resultset_type'  => 'array',
        'auto_timestamp'  => false,
        'datetime_format' => 'Y-m-d H:i:s',
        'sql_explain'     => false,
    ],
];
