<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的作品 - 个性化海报制作</title>
    <link rel="stylesheet" href="/static/layui/css/layui.css">
    <style>
        body { background: #f5f5f5; }
        .header { background: white; padding: 20px 0; margin-bottom: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; display: flex; justify-content: space-between; align-items: center; }
        .header h1 { margin: 0; color: #333; font-size: 24px; }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
        .filter-bar { background: white; border-radius: 8px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .works-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; }
        .work-card { background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 12px rgba(0,0,0,0.1); transition: transform 0.3s; }
        .work-card:hover { transform: translateY(-3px); }
        .work-preview { position: relative; height: 200px; background: #f8f8f8; display: flex; align-items: center; justify-content: center; }
        .work-preview iframe { width: 100%; height: 100%; border: none; }
        .work-preview img { max-width: 100%; max-height: 100%; object-fit: contain; }
        .work-preview .placeholder { color: #999; text-align: center; }
        .work-info { padding: 15px; }
        .work-title { margin: 0 0 8px 0; font-size: 16px; font-weight: bold; color: #333; }
        .work-meta { font-size: 12px; color: #999; margin-bottom: 10px; }
        .work-status { display: inline-block; padding: 2px 8px; border-radius: 12px; font-size: 11px; }
        .status-draft { background: #fff3cd; color: #856404; }
        .status-published { background: #d4edda; color: #155724; }
        .work-actions { padding: 0 15px 15px 15px; }
        .work-actions .layui-btn { margin-right: 5px; margin-bottom: 5px; }
        .empty-state { text-align: center; padding: 60px 20px; color: #999; }
        .empty-state i { font-size: 64px; margin-bottom: 20px; display: block; }
        .pagination { text-align: center; margin-top: 30px; }
    </style>
</head>
<body>
    <!-- 头部 -->
    <div class="header">
        <div class="container">
            <h1>我的作品</h1>
            <div>
                <a href="/index/poster_template/index" class="layui-btn layui-btn-primary">
                    <i class="layui-icon layui-icon-return"></i>返回首页
                </a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <!-- 筛选栏 -->
        <div class="filter-bar">
            <form class="layui-form" lay-filter="filter-form">
                <div class="layui-row layui-col-space10">
                    <div class="layui-col-md3">
                        <select name="is_draft" lay-search>
                            <option value="">全部状态</option>
                            <option value="1">草稿</option>
                            <option value="0">已发布</option>
                        </select>
                    </div>
                    <div class="layui-col-md3">
                        <select name="config_id" lay-search>
                            <option value="">全部模板</option>
                        </select>
                    </div>
                    <div class="layui-col-md3">
                        <select name="sort">
                            <option value="updated_at desc">最近更新</option>
                            <option value="created_at desc">创建时间</option>
                            <option value="created_at asc">创建时间（旧到新）</option>
                        </select>
                    </div>
                    <div class="layui-col-md3">
                        <button type="button" class="layui-btn" id="search-btn">
                            <i class="layui-icon layui-icon-search"></i>搜索
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- 作品列表 -->
        <div id="works-container">
            <div class="works-grid" id="works-grid">
                <!-- 作品卡片将在这里动态生成 -->
            </div>
            
            <!-- 分页 -->
            <div class="pagination" id="pagination-container"></div>
        </div>
        
        <!-- 加载状态 -->
        <div id="loading-state" style="text-align: center; padding: 40px; display: none;">
            <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 32px;"></i>
            <p>正在加载...</p>
        </div>
        
        <!-- 空状态 -->
        <div id="empty-state" class="empty-state" style="display: none;">
            <i class="layui-icon layui-icon-picture"></i>
            <h3>还没有作品</h3>
            <p>快去制作你的第一个个性化海报吧！</p>
            <a href="/index/poster_template/index" class="layui-btn">
                <i class="layui-icon layui-icon-add-1"></i>开始制作
            </a>
        </div>
    </div>

    <script src="/static/layui/layui.js"></script>
    <script>
        layui.use(['form', 'layer', 'laypage'], function(){
            var form = layui.form;
            var layer = layui.layer;
            var laypage = layui.laypage;
            
            var currentPage = 1;
            var pageSize = 12;
            var currentFilters = {};
            
            // 初始化
            init();
            
            function init() {
                loadWorks();
                bindEvents();
            }
            
            // 绑定事件
            function bindEvents() {
                // 搜索按钮
                document.getElementById('search-btn').addEventListener('click', function() {
                    currentPage = 1;
                    loadWorks();
                });
                
                // 表单重置
                form.on('submit(filter-form)', function(data) {
                    currentFilters = data.field;
                    currentPage = 1;
                    loadWorks();
                    return false;
                });
            }
            
            // 加载作品列表
            function loadWorks() {
                showLoading();
                
                var params = Object.assign({
                    page: currentPage,
                    pageSize: pageSize
                }, currentFilters);
                
                $.get('/index/poster_template/get_user_data_list', params, function(res) {
                    hideLoading();
                    
                    if (res.code == 1) {
                        renderWorks(res.data.list);
                        renderPagination(res.data);
                    } else {
                        showError('加载失败：' + res.msg);
                    }
                }).fail(function() {
                    hideLoading();
                    showError('网络错误，请重试');
                });
            }
            
            // 渲染作品列表
            function renderWorks(works) {
                var container = document.getElementById('works-grid');
                
                if (!works || works.length === 0) {
                    container.style.display = 'none';
                    document.getElementById('empty-state').style.display = 'block';
                    document.getElementById('pagination-container').innerHTML = '';
                    return;
                }
                
                container.style.display = 'grid';
                document.getElementById('empty-state').style.display = 'none';
                
                var html = '';
                works.forEach(function(work) {
                    html += generateWorkCard(work);
                });
                
                container.innerHTML = html;
            }
            
            // 生成作品卡片
            function generateWorkCard(work) {
                var statusClass = work.is_draft ? 'status-draft' : 'status-published';
                var statusText = work.is_draft ? '草稿' : '已发布';
                
                var previewContent = '';
                if (work.preview_url) {
                    previewContent = '<iframe src="' + work.preview_url + '"></iframe>';
                } else if (work.generated_image_url) {
                    previewContent = '<img src="' + work.generated_image_url + '" alt="作品预览">';
                } else {
                    previewContent = '<div class="placeholder"><i class="layui-icon layui-icon-picture" style="font-size: 48px;"></i><p>暂无预览</p></div>';
                }
                
                return '<div class="work-card">' +
                       '<div class="work-preview">' + previewContent + '</div>' +
                       '<div class="work-info">' +
                       '<h3 class="work-title">' + (work.config ? work.config.config_name : '未知模板') + '</h3>' +
                       '<div class="work-meta">' +
                       '<span class="work-status ' + statusClass + '">' + statusText + '</span> ' +
                       '<span>创建于 ' + formatDate(work.created_at) + '</span>' +
                       '</div>' +
                       '</div>' +
                       '<div class="work-actions">' +
                       '<button class="layui-btn layui-btn-xs" onclick="editWork(\'' + work.id + '\')">编辑</button>' +
                       (work.preview_url ? '<button class="layui-btn layui-btn-xs layui-btn-normal" onclick="viewPreview(\'' + work.preview_url + '\')">预览</button>' : '') +
                       (!work.is_draft ? '<button class="layui-btn layui-btn-xs layui-btn-warm" onclick="generateImage(\'' + work.id + '\')">生成图片</button>' : '') +
                       '<button class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteWork(\'' + work.id + '\')">删除</button>' +
                       '</div>' +
                       '</div>';
            }
            
            // 渲染分页
            function renderPagination(data) {
                if (data.totalPages <= 1) {
                    document.getElementById('pagination-container').innerHTML = '';
                    return;
                }
                
                laypage.render({
                    elem: 'pagination-container',
                    count: data.total,
                    limit: pageSize,
                    curr: currentPage,
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                    jump: function(obj, first) {
                        if (!first) {
                            currentPage = obj.curr;
                            pageSize = obj.limit;
                            loadWorks();
                        }
                    }
                });
            }
            
            // 显示加载状态
            function showLoading() {
                document.getElementById('loading-state').style.display = 'block';
                document.getElementById('works-container').style.display = 'none';
            }
            
            // 隐藏加载状态
            function hideLoading() {
                document.getElementById('loading-state').style.display = 'none';
                document.getElementById('works-container').style.display = 'block';
            }
            
            // 显示错误
            function showError(message) {
                layer.msg(message, {icon: 2});
            }
            
            // 格式化日期
            function formatDate(dateStr) {
                var date = new Date(dateStr);
                return date.getFullYear() + '-' + 
                       String(date.getMonth() + 1).padStart(2, '0') + '-' + 
                       String(date.getDate()).padStart(2, '0') + ' ' +
                       String(date.getHours()).padStart(2, '0') + ':' +
                       String(date.getMinutes()).padStart(2, '0');
            }
            
            // 全局函数
            window.editWork = function(workId) {
                // 跳转到编辑页面
                window.location.href = '/index/poster_template/edit/' + workId;
            };
            
            window.viewPreview = function(previewUrl) {
                layer.open({
                    type: 2,
                    title: '预览效果',
                    area: ['80%', '80%'],
                    content: previewUrl
                });
            };
            
            window.generateImage = function(workId) {
                layer.confirm('确定要生成图片吗？', function(index) {
                    var loadingIndex = layer.load(2, {content: '正在生成图片...'});
                    
                    $.post('/index/poster_template/generate_image', {
                        data_id: workId,
                        width: 1242,
                        height: 2208,
                        quality: 0.9
                    }, function(res) {
                        layer.close(loadingIndex);
                        
                        if (res.code == 1) {
                            layer.msg('图片生成成功', {icon: 1});
                            loadWorks(); // 刷新列表
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }).fail(function() {
                        layer.close(loadingIndex);
                        layer.msg('生成失败', {icon: 2});
                    });
                    
                    layer.close(index);
                });
            };
            
            window.deleteWork = function(workId) {
                layer.confirm('确定要删除这个作品吗？删除后不可恢复！', function(index) {
                    var loadingIndex = layer.load(2, {content: '正在删除...'});
                    
                    $.post('/index/poster_template/delete_data', {data_id: workId}, function(res) {
                        layer.close(loadingIndex);
                        
                        if (res.code == 1) {
                            layer.msg('删除成功', {icon: 1});
                            loadWorks(); // 刷新列表
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }).fail(function() {
                        layer.close(loadingIndex);
                        layer.msg('删除失败', {icon: 2});
                    });
                    
                    layer.close(index);
                });
            };
        });
    </script>
</body>
</html>
