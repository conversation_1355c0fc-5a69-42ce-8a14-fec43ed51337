<?php
/**
 * 动态参数模板系统种子数据生成器
 */

namespace test\poster_template\tools\seeders;

use app\common\model\PosterTemplateConfig;
use app\common\model\PosterUserData;
use app\common\model\PosterGenerationRecord;

class PosterTemplateSeeder
{
    /**
     * 运行种子数据生成
     */
    public static function run()
    {
        echo "开始生成动态参数模板系统种子数据...\n";
        
        // 清理现有测试数据
        self::cleanTestData();
        
        // 生成模板配置数据
        self::seedTemplateConfigs();
        
        // 生成用户数据
        self::seedUserData();
        
        // 生成生成记录数据
        self::seedGenerationRecords();
        
        echo "种子数据生成完成！\n";
        self::showStats();
    }
    
    /**
     * 清理测试数据
     */
    protected static function cleanTestData()
    {
        echo "清理现有测试数据...\n";
        
        PosterGenerationRecord::where('id', 'like', 'demo_%')->delete();
        PosterUserData::where('id', 'like', 'demo_%')->delete();
        PosterTemplateConfig::where('id', 'like', 'demo_%')->delete();
    }
    
    /**
     * 生成模板配置数据
     */
    protected static function seedTemplateConfigs()
    {
        echo "生成模板配置数据...\n";
        
        $configs = [
            [
                'id' => 'demo_config_001',
                'template_id' => '1',
                'template_title' => '日签海报模板',
                'config_name' => '个性化日签配置',
                'config_description' => '用于生成个性化日签的参数配置',
                'parameters' => [
                    [
                        'id' => 'param_001',
                        'elementUuid' => 'uuid_greeting',
                        'parameterName' => 'greeting',
                        'parameterLabel' => '问候语',
                        'parameterType' => 'text',
                        'isRequired' => true,
                        'defaultValue' => '你好，世界',
                        'validationRules' => ['maxLength' => 50],
                        'displayOrder' => 1,
                        'isEnabled' => true,
                        'parameterDescription' => '显示在海报顶部的问候语'
                    ],
                    [
                        'id' => 'param_002',
                        'elementUuid' => 'uuid_quote',
                        'parameterName' => 'daily_quote',
                        'parameterLabel' => '每日金句',
                        'parameterType' => 'textarea',
                        'isRequired' => true,
                        'defaultValue' => '成功不是终点，失败不是致命的，重要的是继续前进的勇气。',
                        'validationRules' => ['maxLength' => 200],
                        'displayOrder' => 2,
                        'isEnabled' => true,
                        'parameterDescription' => '显示在海报中央的励志金句'
                    ],
                    [
                        'id' => 'param_003',
                        'elementUuid' => 'uuid_date',
                        'parameterName' => 'display_date',
                        'parameterLabel' => '显示日期',
                        'parameterType' => 'text',
                        'isRequired' => false,
                        'defaultValue' => date('Y年m月d日'),
                        'validationRules' => ['maxLength' => 20],
                        'displayOrder' => 3,
                        'isEnabled' => true,
                        'parameterDescription' => '显示在海报底部的日期'
                    ]
                ],
                'created_by' => 'seeder',
                'status' => 1
            ],
            [
                'id' => 'demo_config_002',
                'template_id' => '2',
                'template_title' => '商品宣传海报',
                'config_name' => '商品推广配置',
                'config_description' => '用于商品宣传推广的参数配置',
                'parameters' => [
                    [
                        'id' => 'param_004',
                        'elementUuid' => 'uuid_product_name',
                        'parameterName' => 'product_name',
                        'parameterLabel' => '商品名称',
                        'parameterType' => 'text',
                        'isRequired' => true,
                        'defaultValue' => '精选好物',
                        'validationRules' => ['maxLength' => 100],
                        'displayOrder' => 1,
                        'isEnabled' => true,
                        'parameterDescription' => '商品的名称标题'
                    ],
                    [
                        'id' => 'param_005',
                        'elementUuid' => 'uuid_price',
                        'parameterName' => 'price',
                        'parameterLabel' => '商品价格',
                        'parameterType' => 'text',
                        'isRequired' => true,
                        'defaultValue' => '¥99.00',
                        'validationRules' => ['maxLength' => 20],
                        'displayOrder' => 2,
                        'isEnabled' => true,
                        'parameterDescription' => '商品的销售价格'
                    ],
                    [
                        'id' => 'param_006',
                        'elementUuid' => 'uuid_description',
                        'parameterName' => 'description',
                        'parameterLabel' => '商品描述',
                        'parameterType' => 'textarea',
                        'isRequired' => false,
                        'defaultValue' => '品质保证，值得信赖',
                        'validationRules' => ['maxLength' => 150],
                        'displayOrder' => 3,
                        'isEnabled' => true,
                        'parameterDescription' => '商品的详细描述'
                    ],
                    [
                        'id' => 'param_007',
                        'elementUuid' => 'uuid_contact',
                        'parameterName' => 'contact_info',
                        'parameterLabel' => '联系方式',
                        'parameterType' => 'phone',
                        'isRequired' => false,
                        'defaultValue' => '138-0000-0000',
                        'validationRules' => ['maxLength' => 20],
                        'displayOrder' => 4,
                        'isEnabled' => true,
                        'parameterDescription' => '商家联系电话'
                    ]
                ],
                'created_by' => 'seeder',
                'status' => 1
            ]
        ];
        
        foreach ($configs as $config) {
            PosterTemplateConfig::create($config);
            echo "  - 创建配置: {$config['config_name']}\n";
        }
    }
    
    /**
     * 生成用户数据
     */
    protected static function seedUserData()
    {
        echo "生成用户数据...\n";
        
        $userData = [
            [
                'id' => 'demo_data_001',
                'config_id' => 'demo_config_001',
                'user_id' => 'user_001',
                'session_id' => null,
                'parameter_values' => [
                    'greeting' => '你好，新年快乐',
                    'daily_quote' => '新年新气象，愿所有美好如期而至，愿所有努力都有收获！',
                    'display_date' => '2025年1月16日'
                ],
                'is_draft' => false,
                'preview_url' => 'http://localhost:7001/preview/parameter/demo_data_001',
                'generated_image_url' => 'http://localhost:7001/static/generated/demo_data_001.jpg'
            ],
            [
                'id' => 'demo_data_002',
                'config_id' => 'demo_config_001',
                'user_id' => null,
                'session_id' => 'session_001',
                'parameter_values' => [
                    'greeting' => '早安，世界',
                    'daily_quote' => '每一个清晨都是新的开始，带着希望迎接美好的一天。',
                    'display_date' => date('Y年m月d日')
                ],
                'is_draft' => true,
                'preview_url' => null,
                'generated_image_url' => null
            ],
            [
                'id' => 'demo_data_003',
                'config_id' => 'demo_config_002',
                'user_id' => 'user_002',
                'session_id' => null,
                'parameter_values' => [
                    'product_name' => '超值优选商品套装',
                    'price' => '¥199.00',
                    'description' => '精心挑选的优质商品，品质保证，限时特惠！',
                    'contact_info' => '138-8888-8888'
                ],
                'is_draft' => false,
                'preview_url' => 'http://localhost:7001/preview/parameter/demo_data_003',
                'generated_image_url' => 'http://localhost:7001/static/generated/demo_data_003.jpg'
            ]
        ];
        
        foreach ($userData as $data) {
            PosterUserData::create($data);
            echo "  - 创建用户数据: {$data['id']}\n";
        }
    }
    
    /**
     * 生成生成记录数据
     */
    protected static function seedGenerationRecords()
    {
        echo "生成生成记录数据...\n";
        
        $records = [
            [
                'id' => 'demo_record_001',
                'data_id' => 'demo_data_001',
                'image_url' => 'http://localhost:7001/static/generated/demo_data_001.jpg',
                'generation_options' => [
                    'width' => 1242,
                    'height' => 2208,
                    'quality' => 0.9,
                    'format' => 'jpg'
                ],
                'generation_time' => 2.345,
                'file_size' => 856432,
                'image_width' => 1242,
                'image_height' => 2208,
                'image_format' => 'jpg',
                'quality' => 0.90,
                'status' => 1,
                'error_message' => null
            ],
            [
                'id' => 'demo_record_002',
                'data_id' => 'demo_data_003',
                'image_url' => 'http://localhost:7001/static/generated/demo_data_003.jpg',
                'generation_options' => [
                    'width' => 800,
                    'height' => 1200,
                    'quality' => 0.8,
                    'format' => 'jpg'
                ],
                'generation_time' => 1.876,
                'file_size' => 654321,
                'image_width' => 800,
                'image_height' => 1200,
                'image_format' => 'jpg',
                'quality' => 0.80,
                'status' => 1,
                'error_message' => null
            ],
            [
                'id' => 'demo_record_003',
                'data_id' => 'demo_data_001',
                'image_url' => '',
                'generation_options' => [
                    'width' => 1242,
                    'height' => 2208,
                    'quality' => 0.9,
                    'format' => 'png'
                ],
                'generation_time' => 5.123,
                'file_size' => 0,
                'image_width' => 0,
                'image_height' => 0,
                'image_format' => '',
                'quality' => 0.00,
                'status' => 0,
                'error_message' => '生成超时：API响应时间过长'
            ]
        ];
        
        foreach ($records as $record) {
            PosterGenerationRecord::create($record);
            echo "  - 创建生成记录: {$record['id']}\n";
        }
    }
    
    /**
     * 显示统计信息
     */
    protected static function showStats()
    {
        $configCount = PosterTemplateConfig::where('id', 'like', 'demo_%')->count();
        $dataCount = PosterUserData::where('id', 'like', 'demo_%')->count();
        $recordCount = PosterGenerationRecord::where('id', 'like', 'demo_%')->count();
        
        echo "\n种子数据统计:\n";
        echo "  - 模板配置: {$configCount} 条\n";
        echo "  - 用户数据: {$dataCount} 条\n";
        echo "  - 生成记录: {$recordCount} 条\n";
    }
}
