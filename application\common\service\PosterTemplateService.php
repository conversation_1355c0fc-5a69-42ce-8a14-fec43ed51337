<?php
/**
 * 动态参数模板系统 - 模板管理服务
 */

namespace app\common\service;

use app\common\model\PosterTemplateConfig;
use app\common\model\PosterUserData;
use think\facade\Log;

class PosterTemplateService
{
    protected $apiClient;
    
    public function __construct()
    {
        $this->apiClient = new PosterApiClient();
    }
    
    /**
     * 获取模板列表
     * @param array $params
     * @return array
     */
    public function getTemplateList($params = [])
    {
        try {
            $response = $this->apiClient->getTemplates($params);
            
            if (!$response['success']) {
                throw new \Exception($response['error'] ?? 'Failed to get templates');
            }
            
            return [
                'success' => true,
                'data' => $response['data']['data'] ?? []
            ];
            
        } catch (\Exception $e) {
            Log::error('Get template list failed', [
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 获取模板详情
     * @param string $templateId
     * @return array
     */
    public function getTemplateDetail($templateId)
    {
        try {
            $response = $this->apiClient->getTemplate($templateId);
            
            if (!$response['success']) {
                throw new \Exception($response['error'] ?? 'Failed to get template');
            }
            
            return [
                'success' => true,
                'data' => $response['data']['data'] ?? []
            ];
            
        } catch (\Exception $e) {
            Log::error('Get template detail failed', [
                'template_id' => $templateId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 解析模板并创建配置
     * @param string $templateId
     * @param string $configName
     * @param string $configDescription
     * @param string $createdBy
     * @return array
     */
    public function parseAndCreateConfig($templateId, $configName, $configDescription = '', $createdBy = '')
    {
        try {
            // 1. 解析模板
            $parseResponse = $this->apiClient->parseTemplate($templateId);
            
            if (!$parseResponse['success']) {
                throw new \Exception($parseResponse['error'] ?? 'Failed to parse template');
            }
            
            $parseData = $parseResponse['data']['data'] ?? [];
            
            // 2. 创建配置记录
            $configData = [
                'template_id' => $templateId,
                'template_title' => $parseData['templateTitle'] ?? '',
                'config_name' => $configName,
                'config_description' => $configDescription,
                'parameters' => $parseData['parameterCandidates'] ?? [],
                'created_by' => $createdBy,
                'status' => PosterTemplateConfig::STATUS_ENABLED
            ];
            
            $config = PosterTemplateConfig::createConfig($configData);
            
            if (!$config) {
                throw new \Exception('Failed to create template config');
            }
            
            Log::info('Template config created', [
                'config_id' => $config->id,
                'template_id' => $templateId,
                'created_by' => $createdBy
            ]);
            
            return [
                'success' => true,
                'data' => [
                    'configId' => $config->id,
                    'templateId' => $templateId,
                    'configName' => $configName,
                    'parameterCandidates' => $parseData['parameterCandidates'] ?? [],
                    'summary' => $parseData['summary'] ?? []
                ]
            ];
            
        } catch (\Exception $e) {
            Log::error('Parse and create config failed', [
                'template_id' => $templateId,
                'config_name' => $configName,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 更新模板配置
     * @param string $configId
     * @param array $updateData
     * @return array
     */
    public function updateConfig($configId, $updateData)
    {
        try {
            // 验证参数结构
            if (isset($updateData['parameters']) && 
                !PosterTemplateConfig::validateParameters($updateData['parameters'])) {
                throw new \Exception('Invalid parameters structure');
            }
            
            $result = PosterTemplateConfig::updateConfig($configId, $updateData);
            
            if (!$result) {
                throw new \Exception('Config not found or update failed');
            }
            
            Log::info('Template config updated', [
                'config_id' => $configId,
                'updated_fields' => array_keys($updateData)
            ]);
            
            return [
                'success' => true,
                'data' => ['configId' => $configId]
            ];
            
        } catch (\Exception $e) {
            Log::error('Update config failed', [
                'config_id' => $configId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 获取配置列表
     * @param array $filters
     * @param int $page
     * @param int $pageSize
     * @return array
     */
    public function getConfigList($filters = [], $page = 1, $pageSize = 15)
    {
        try {
            $where = [];
            
            // 处理筛选条件
            if (!empty($filters['template_id'])) {
                $where['template_id'] = $filters['template_id'];
            }
            if (!empty($filters['created_by'])) {
                $where['created_by'] = $filters['created_by'];
            }
            if (isset($filters['status'])) {
                $where['status'] = $filters['status'];
            } else {
                $where['status'] = PosterTemplateConfig::STATUS_ENABLED;
            }
            
            // 搜索关键词
            $query = PosterTemplateConfig::where($where);
            if (!empty($filters['keyword'])) {
                $keyword = $filters['keyword'];
                $query->where(function($q) use ($keyword) {
                    $q->whereLike('config_name', "%{$keyword}%")
                      ->whereOr('template_title', 'like', "%{$keyword}%")
                      ->whereOr('config_description', 'like', "%{$keyword}%");
                });
            }
            
            // 分页查询
            $total = $query->count();
            $list = $query->order('created_at desc')
                         ->page($page, $pageSize)
                         ->select();
            
            // 添加统计信息
            foreach ($list as &$item) {
                $item['parameter_stats'] = $item->getParameterStats();
                $item['user_data_count'] = PosterUserData::where('config_id', $item->id)->count();
            }
            
            return [
                'success' => true,
                'data' => [
                    'list' => $list,
                    'total' => $total,
                    'page' => $page,
                    'pageSize' => $pageSize,
                    'totalPages' => ceil($total / $pageSize)
                ]
            ];
            
        } catch (\Exception $e) {
            Log::error('Get config list failed', [
                'filters' => $filters,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 获取配置详情
     * @param string $configId
     * @return array
     */
    public function getConfigDetail($configId)
    {
        try {
            $config = PosterTemplateConfig::where('id', $configId)->find();
            
            if (!$config) {
                throw new \Exception('Config not found');
            }
            
            // 添加统计信息
            $config['parameter_stats'] = $config->getParameterStats();
            $config['user_data_stats'] = PosterUserData::getDataStats($configId);
            
            return [
                'success' => true,
                'data' => $config
            ];
            
        } catch (\Exception $e) {
            Log::error('Get config detail failed', [
                'config_id' => $configId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 删除配置
     * @param string $configId
     * @return array
     */
    public function deleteConfig($configId)
    {
        try {
            // 检查是否有关联的用户数据
            $userDataCount = PosterUserData::where('config_id', $configId)->count();
            if ($userDataCount > 0) {
                throw new \Exception("Cannot delete config with {$userDataCount} user data records");
            }
            
            $result = PosterTemplateConfig::deleteConfig($configId);
            
            if (!$result) {
                throw new \Exception('Config not found or delete failed');
            }
            
            Log::info('Template config deleted', ['config_id' => $configId]);
            
            return [
                'success' => true,
                'data' => ['configId' => $configId]
            ];
            
        } catch (\Exception $e) {
            Log::error('Delete config failed', [
                'config_id' => $configId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 复制配置
     * @param string $configId
     * @param string $newConfigName
     * @param string $createdBy
     * @return array
     */
    public function copyConfig($configId, $newConfigName, $createdBy = '')
    {
        try {
            $originalConfig = PosterTemplateConfig::where('id', $configId)->find();
            
            if (!$originalConfig) {
                throw new \Exception('Original config not found');
            }
            
            $newConfigData = [
                'template_id' => $originalConfig->template_id,
                'template_title' => $originalConfig->template_title,
                'config_name' => $newConfigName,
                'config_description' => $originalConfig->config_description . ' (复制)',
                'parameters' => $originalConfig->parameters,
                'created_by' => $createdBy,
                'status' => PosterTemplateConfig::STATUS_ENABLED
            ];
            
            $newConfig = PosterTemplateConfig::createConfig($newConfigData);
            
            if (!$newConfig) {
                throw new \Exception('Failed to create copied config');
            }
            
            Log::info('Template config copied', [
                'original_config_id' => $configId,
                'new_config_id' => $newConfig->id,
                'created_by' => $createdBy
            ]);
            
            return [
                'success' => true,
                'data' => [
                    'configId' => $newConfig->id,
                    'configName' => $newConfigName
                ]
            ];
            
        } catch (\Exception $e) {
            Log::error('Copy config failed', [
                'config_id' => $configId,
                'new_config_name' => $newConfigName,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
