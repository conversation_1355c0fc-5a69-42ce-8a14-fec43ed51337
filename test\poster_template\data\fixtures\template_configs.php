<?php
/**
 * 模板配置测试数据
 */

return [
    [
        'id' => 'test-config-001',
        'template_id' => '1',
        'template_title' => '测试模板1 - 日签海报',
        'config_name' => '日签配置1',
        'config_description' => '用于测试的日签配置',
        'parameters' => json_encode([
            [
                'id' => 'param-001',
                'elementUuid' => 'test-uuid-001',
                'parameterName' => 'greeting',
                'parameterLabel' => '问候语',
                'parameterType' => 'text',
                'isRequired' => true,
                'defaultValue' => '你好，世界',
                'validationRules' => ['maxLength' => 50],
                'displayOrder' => 1,
                'isEnabled' => true,
            ],
            [
                'id' => 'param-002',
                'elementUuid' => 'test-uuid-002',
                'parameterName' => 'content',
                'parameterLabel' => '内容',
                'parameterType' => 'textarea',
                'isRequired' => false,
                'defaultValue' => '这是默认内容',
                'validationRules' => ['maxLength' => 200],
                'displayOrder' => 2,
                'isEnabled' => true,
            ],
        ]),
        'created_by' => 'test-admin',
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s'),
        'status' => 1,
    ],
    [
        'id' => 'test-config-002',
        'template_id' => '2',
        'template_title' => '测试模板2 - 商品宣传',
        'config_name' => '商品宣传配置1',
        'config_description' => '用于测试的商品宣传配置',
        'parameters' => json_encode([
            [
                'id' => 'param-003',
                'elementUuid' => 'test-uuid-003',
                'parameterName' => 'product_name',
                'parameterLabel' => '商品名称',
                'parameterType' => 'text',
                'isRequired' => true,
                'defaultValue' => '测试商品',
                'validationRules' => ['maxLength' => 100],
                'displayOrder' => 1,
                'isEnabled' => true,
            ],
            [
                'id' => 'param-004',
                'elementUuid' => 'test-uuid-004',
                'parameterName' => 'price',
                'parameterLabel' => '价格',
                'parameterType' => 'text',
                'isRequired' => true,
                'defaultValue' => '¥99.00',
                'validationRules' => ['maxLength' => 20],
                'displayOrder' => 2,
                'isEnabled' => true,
            ],
        ]),
        'created_by' => 'test-admin',
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s'),
        'status' => 1,
    ],
];
