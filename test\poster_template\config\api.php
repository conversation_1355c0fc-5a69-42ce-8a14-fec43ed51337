<?php
/**
 * 动态参数模板系统API测试配置
 */

return [
    // 迅排设计API配置
    'poster_api' => [
        'base_url' => env('TEST_POSTER_API_URL', 'http://localhost:7001'),
        'api_key' => env('TEST_POSTER_API_KEY', 'test-api-key'),
        'timeout' => 30,
        'retry_times' => 3,
        'retry_delay' => 1000, // 毫秒
    ],
    
    // Mock API配置
    'mock_api' => [
        'enabled' => env('TEST_MOCK_ENABLED', true),
        'base_url' => 'http://mock.localhost:7001',
        'response_delay' => 100, // 毫秒，模拟网络延迟
    ],
    
    // 测试用例配置
    'test_cases' => [
        'template_ids' => ['1', '2', '3'], // 测试用模板ID
        'batch_size' => 5, // 批量测试大小
        'timeout_limit' => 60, // 超时限制（秒）
    ],
    
    // 外部API测试配置
    'external_api' => [
        'base_url' => env('TEST_EXTERNAL_API_URL', 'http://localhost:8000'),
        'api_key' => env('TEST_EXTERNAL_API_KEY', 'test-external-key'),
        'endpoints' => [
            'parameter_data' => '/api/external/parameter-data',
            'parameter_config' => '/api/external/parameter-config',
            'health' => '/api/external/health',
        ],
    ],
    
    // 图片生成测试配置
    'image_generation' => [
        'test_sizes' => [
            ['width' => 400, 'height' => 600],
            ['width' => 800, 'height' => 1200],
            ['width' => 1242, 'height' => 2208],
        ],
        'test_qualities' => [0.5, 0.8, 0.9],
        'max_file_size' => 5 * 1024 * 1024, // 5MB
        'allowed_formats' => ['jpg', 'png', 'webp'],
    ],
];
