ALTER TABLE `ls_goods`
ADD COLUMN `goods_size` VARCHAR(255) NULL DEFAULT '' COMMENT '商品尺寸' AFTER `is_selffetch`,
ADD COLUMN `price_unit` VARCHAR(50) NULL DEFAULT '' COMMENT '价格单位' AFTER `goods_size`,
ADD COLUMN `sale_start_time` INT(11) NULL DEFAULT 0 COMMENT '起售时间' AFTER `price_unit`; 

-- 在goods_comment表中添加created_by_user_id字段，用于记录创建虚拟评价的用户ID
ALTER TABLE `ls_goods_comment`
ADD COLUMN `created_by_user_id` int(11) DEFAULT NULL COMMENT '创建虚拟评价的用户ID' AFTER `virtual_data`;

-- ============================================================================
-- 动态参数模板系统数据库表创建
-- 创建时间: 2025-01-16
-- 描述: 为动态参数模板系统创建所需的数据库表
-- ============================================================================

-- 1. 创建模板参数配置表
CREATE TABLE IF NOT EXISTS `ls_poster_template_configs` (
  `id` VARCHAR(32) NOT NULL COMMENT '配置ID',
  `template_id` VARCHAR(32) NOT NULL COMMENT '迅排设计模板ID',
  `template_title` VARCHAR(255) NULL COMMENT '模板标题',
  `config_name` VARCHAR(255) NOT NULL COMMENT '配置名称',
  `config_description` TEXT NULL COMMENT '配置描述',
  `parameters` JSON NOT NULL COMMENT '参数定义JSON',
  `created_by` VARCHAR(32) NULL COMMENT '创建者ID',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` TINYINT(1) DEFAULT 1 COMMENT '状态：1启用 0禁用',

  PRIMARY KEY (`id`),
  INDEX `idx_template_id` (`template_id`),
  INDEX `idx_created_by` (`created_by`),
  INDEX `idx_status` (`status`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模板参数配置表';

-- 2. 创建用户参数数据表
CREATE TABLE IF NOT EXISTS `ls_poster_user_data` (
  `id` VARCHAR(32) NOT NULL COMMENT '数据ID',
  `config_id` VARCHAR(32) NOT NULL COMMENT '配置ID',
  `user_id` VARCHAR(32) NULL COMMENT '用户ID',
  `session_id` VARCHAR(64) NULL COMMENT '会话ID（匿名用户）',
  `parameter_values` JSON NOT NULL COMMENT '用户填写的参数值',
  `is_draft` BOOLEAN DEFAULT TRUE COMMENT '是否为草稿',
  `preview_url` VARCHAR(500) NULL COMMENT '预览页面URL',
  `generated_image_url` VARCHAR(500) NULL COMMENT '生成的图片URL',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  PRIMARY KEY (`id`),
  INDEX `idx_config_id` (`config_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_session_id` (`session_id`),
  INDEX `idx_is_draft` (`is_draft`),
  INDEX `idx_created_at` (`created_at`),
  INDEX `idx_user_draft` (`user_id`, `is_draft`),
  INDEX `idx_session_draft` (`session_id`, `is_draft`),

  CONSTRAINT `fk_poster_user_data_config`
    FOREIGN KEY (`config_id`)
    REFERENCES `ls_poster_template_configs`(`id`)
    ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户参数数据表';

-- 3. 创建图片生成记录表
CREATE TABLE IF NOT EXISTS `ls_poster_generation_records` (
  `id` VARCHAR(32) NOT NULL COMMENT '记录ID',
  `data_id` VARCHAR(32) NOT NULL COMMENT '参数数据ID',
  `image_url` VARCHAR(500) NOT NULL COMMENT '生成的图片URL',
  `generation_options` JSON NULL COMMENT '生成选项（宽度、高度、质量等）',
  `generation_time` DECIMAL(10,3) NULL COMMENT '生成耗时（秒）',
  `file_size` INT NULL COMMENT '文件大小（字节）',
  `image_width` INT NULL COMMENT '图片宽度',
  `image_height` INT NULL COMMENT '图片高度',
  `image_format` VARCHAR(10) NULL COMMENT '图片格式（jpg、png等）',
  `quality` DECIMAL(3,2) NULL COMMENT '图片质量（0.1-1.0）',
  `status` TINYINT(1) DEFAULT 1 COMMENT '状态：1成功 0失败',
  `error_message` TEXT NULL COMMENT '错误信息（如果生成失败）',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

  PRIMARY KEY (`id`),
  INDEX `idx_data_id` (`data_id`),
  INDEX `idx_created_at` (`created_at`),
  INDEX `idx_status` (`status`),
  INDEX `idx_generation_time` (`generation_time`),
  INDEX `idx_file_size` (`file_size`),
  INDEX `idx_data_status` (`data_id`, `status`),

  CONSTRAINT `fk_poster_generation_records_data`
    FOREIGN KEY (`data_id`)
    REFERENCES `ls_poster_user_data`(`id`)
    ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片生成记录表';

-- ============================================================================
-- 动态参数模板系统菜单数据
-- 创建时间: 2025-08-24
-- 描述: 为动态参数模板系统添加后台管理菜单
-- ============================================================================

-- 获取当前最大的sort值，确保新菜单排在合适位置
SET @max_sort = (SELECT IFNULL(MAX(sort), 0) FROM ls_dev_auth WHERE pid = 0);

-- 添加主菜单：动态参数模板
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(0, 1, 0, '动态参数模板', 'layui-icon-template-1', @max_sort + 10, '', 0, 0, UNIX_TIMESTAMP());

-- 获取刚插入的主菜单ID
SET @main_menu_id = LAST_INSERT_ID();

-- 添加子菜单：模板配置管理
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(@main_menu_id, 1, 0, '模板配置', 'layui-icon-set', 100, 'poster_template/config_list', 0, 0, UNIX_TIMESTAMP());

SET @config_menu_id = LAST_INSERT_ID();

-- 添加子菜单：用户数据管理
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(@main_menu_id, 1, 0, '用户数据', 'layui-icon-user', 90, 'poster_template/user_data_list', 0, 0, UNIX_TIMESTAMP());

-- 添加权限节点：模板配置相关权限
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(@config_menu_id, 2, 0, '查看配置列表', '', 100, 'poster_template/config_list', 0, 0, UNIX_TIMESTAMP()),
(@config_menu_id, 2, 0, '添加配置', '', 90, 'poster_template/config_add', 0, 0, UNIX_TIMESTAMP()),
(@config_menu_id, 2, 0, '编辑配置', '', 80, 'poster_template/config_edit', 0, 0, UNIX_TIMESTAMP()),
(@config_menu_id, 2, 0, '删除配置', '', 70, 'poster_template/config_delete', 0, 0, UNIX_TIMESTAMP());