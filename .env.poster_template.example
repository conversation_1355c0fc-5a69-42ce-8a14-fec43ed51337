# 动态参数模板系统环境变量配置示例
# 复制此文件为 .env.poster_template 并修改相应的配置值

# 迅排设计API配置
POSTER_API_BASE_URL=http://localhost:7001
POSTER_API_TIMEOUT=30
POSTER_API_RETRY_TIMES=3
POSTER_API_RETRY_DELAY=1000

# 外部API配置（供迅排设计调用）
POSTER_EXTERNAL_API_KEY=your-secure-api-key-here
POSTER_EXTERNAL_API_BASE_URL=http://localhost:8000
POSTER_API_RATE_LIMIT=1000
POSTER_EXTERNAL_API_TIMEOUT=30

# 缓存配置
POSTER_CACHE_ENABLED=true
POSTER_TEMPLATE_CACHE_TTL=600
POSTER_PARSE_CACHE_TTL=3600
POSTER_USER_DATA_CACHE_TTL=300

# 图片生成配置
POSTER_MAX_WIDTH=4000
POSTER_MAX_HEIGHT=4000
POSTER_DEFAULT_QUALITY=0.9
POSTER_MAX_FILE_SIZE=10485760

# 批量处理配置
POSTER_BATCH_MAX_ITEMS=50
POSTER_BATCH_TIMEOUT=300
POSTER_BATCH_CONCURRENT_LIMIT=5

# 安全配置
POSTER_ALLOWED_ORIGINS=*
POSTER_MAX_PARAMETER_LENGTH=1000

# 日志配置
POSTER_LOGGING_ENABLED=true
POSTER_LOG_LEVEL=info
POSTER_LOG_MAX_FILES=30

# 开发模式配置
POSTER_MOCK_ENABLED=false
POSTER_DEBUG_MODE=false
POSTER_TEST_MODE=false

# 文件存储配置
POSTER_STORAGE_DISK=local
POSTER_STORAGE_PATH=poster_template
POSTER_STORAGE_URL_PREFIX=/uploads/poster_template

# 测试环境配置
TEST_DB_HOST=127.0.0.1
TEST_DB_NAME=likeshop_test
TEST_DB_USER=root
TEST_DB_PASS=root
TEST_DB_PORT=3306

TEST_POSTER_API_URL=http://localhost:7001
TEST_POSTER_API_KEY=test-api-key
TEST_EXTERNAL_API_URL=http://localhost:8000
TEST_EXTERNAL_API_KEY=test-external-key
TEST_MOCK_ENABLED=true
