<?php
/**
 * 动态参数模板系统配置文件
 */

use think\facade\Env;

return [
    // 迅排设计API配置
    'poster_api' => [
        'base_url' => Env::get('poster.api_base_url', 'http://localhost:7001'),
        'timeout' => Env::get('poster.api_timeout', 30),
        'retry_times' => Env::get('poster.api_retry_times', 3),
        'retry_delay' => Env::get('poster.api_retry_delay', 1000), // 毫秒
    ],
    
    // 外部API配置（供迅排设计调用）
    'external_api' => [
        'api_key' => Env::get('poster.external_api_key', 'your-secure-api-key-here'),
        'base_url' => Env::get('poster.external_api_base_url', 'http://localhost:8000'),
        'rate_limit' => Env::get('poster.api_rate_limit', 1000), // 每秒请求数
        'timeout' => Env::get('poster.external_api_timeout', 30),
    ],
    
    // 缓存配置
    'cache' => [
        'enabled' => Env::get('poster.cache_enabled', true),
        'template_ttl' => Env::get('poster.template_cache_ttl', 600), // 模板缓存10分钟
        'parse_ttl' => Env::get('poster.parse_cache_ttl', 3600), // 解析结果缓存1小时
        'user_data_ttl' => Env::get('poster.user_data_cache_ttl', 300), // 用户数据缓存5分钟
        'prefix' => 'poster_template:',
    ],
    
    // 图片生成配置
    'image_generation' => [
        'max_width' => Env::get('poster.max_width', 4000),
        'max_height' => Env::get('poster.max_height', 4000),
        'default_quality' => Env::get('poster.default_quality', 0.9),
        'allowed_formats' => ['jpg', 'png', 'webp'],
        'max_file_size' => Env::get('poster.max_file_size', 10 * 1024 * 1024), // 10MB
    ],
    
    // 批量处理配置
    'batch_processing' => [
        'max_items' => Env::get('poster.batch_max_items', 50),
        'timeout' => Env::get('poster.batch_timeout', 300), // 5分钟
        'concurrent_limit' => Env::get('poster.batch_concurrent_limit', 5),
    ],
    
    // 安全配置
    'security' => [
        'api_key_header' => 'Authorization',
        'api_key_prefix' => 'Bearer ',
        'allowed_origins' => Env::get('poster.allowed_origins', '*'),
        'max_parameter_length' => Env::get('poster.max_parameter_length', 1000),
    ],
    
    // 日志配置
    'logging' => [
        'enabled' => Env::get('poster.logging_enabled', true),
        'level' => Env::get('poster.log_level', 'info'),
        'file' => 'poster_template.log',
        'max_files' => Env::get('poster.log_max_files', 30),
    ],
    
    // 开发模式配置
    'development' => [
        'mock_enabled' => Env::get('poster.mock_enabled', false),
        'debug_mode' => Env::get('poster.debug_mode', false),
        'test_mode' => Env::get('poster.test_mode', false),
    ],
    
    // 数据库表配置
    'database' => [
        'tables' => [
            'template_configs' => 'poster_template_configs',
            'user_data' => 'poster_user_data',
            'generation_records' => 'poster_generation_records',
        ],
        'prefix' => Env::get('database.prefix', 'ls_'),
    ],
    
    // 文件存储配置
    'storage' => [
        'disk' => Env::get('poster.storage_disk', 'local'),
        'path' => Env::get('poster.storage_path', 'poster_template'),
        'url_prefix' => Env::get('poster.storage_url_prefix', '/uploads/poster_template'),
    ],
];
