# PHP版本要求

## 重要说明

本项目需要在特定PHP版本范围内运行，具体要求如下：

- **PHP版本要求**: 大于7.0且小于8.0 (推荐使用PHP 7.2.9版本)
- **当前测试通过版本**: PHP 7.2.9

## 版本兼容性问题

在PHP 8.0及以上版本可能会遇到以下问题：

1. 函数参数顺序：PHP 8.0强制要求所有必选参数必须在可选参数之前定义
2. 数据类型处理：PHP 8.0对数据类型检查更加严格
3. 弃用功能：PHP 8.0移除了一些在PHP 7.x中弃用的功能

## 已知问题

以下是在使用不兼容PHP版本时可能遇到的具体问题：

- `auto_adapt`函数在PHP 8.0中会报错，因为必选参数`$fontfile`位于可选参数`$angle = 0`之后
- 某些函数可能假设变量是特定类型（如数组），但未进行显式类型检查

## 推荐配置

为确保项目正常运行，建议使用以下配置：

- PHP 7.2.9
- 开启错误报告：`error_reporting(E_ALL)`
- 配置正确的时区：`date.timezone = Asia/Shanghai`

## 依赖库版本

请确保所有依赖库也与您使用的PHP版本兼容。 