<?php
/**
 * 用户参数数据测试数据
 */

return [
    [
        'id' => 'test-data-001',
        'config_id' => 'test-config-001',
        'user_id' => 'test-user-001',
        'session_id' => null,
        'parameter_values' => json_encode([
            'greeting' => '你好，新年快乐',
            'content' => '祝愿大家新年快乐，万事如意！',
        ]),
        'is_draft' => 0,
        'preview_url' => 'http://localhost:7001/preview/parameter/test-data-001',
        'generated_image_url' => null,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s'),
    ],
    [
        'id' => 'test-data-002',
        'config_id' => 'test-config-001',
        'user_id' => null,
        'session_id' => 'test-session-001',
        'parameter_values' => json_encode([
            'greeting' => '你好，春天',
            'content' => '春暖花开，万物复苏',
        ]),
        'is_draft' => 1,
        'preview_url' => null,
        'generated_image_url' => null,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s'),
    ],
    [
        'id' => 'test-data-003',
        'config_id' => 'test-config-002',
        'user_id' => 'test-user-002',
        'session_id' => null,
        'parameter_values' => json_encode([
            'product_name' => '超级好用的测试商品',
            'price' => '¥199.00',
        ]),
        'is_draft' => 0,
        'preview_url' => 'http://localhost:7001/preview/parameter/test-data-003',
        'generated_image_url' => 'http://localhost:7001/static/generated/test-data-003.jpg',
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s'),
    ],
];
