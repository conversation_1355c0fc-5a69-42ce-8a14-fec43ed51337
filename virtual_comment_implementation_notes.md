# 移动端虚拟评价功能实现注意事项

## 数据库调整
1. 需要执行 `virtual_comment_update.sql` 文件中的SQL语句，添加以下字段：
   - `ls_goods_comment` 表添加 `created_by_user_id` 字段，用于记录创建虚拟评价的用户

2. 系统中已创建"评论家"会员等级（成长值要求999999999），用于控制虚拟评价权限

## 权限控制
1. 权限控制基于会员等级实现，只有"评论家"等级的用户才能添加虚拟评价
2. 每个"评论家"每天可添加的虚拟评价数量上限为99条
3. 虚拟评价的添加会记录创建者ID，方便后续管理

## 代码实现
1. 虚拟评价与真实评价共用同一个数据表 `ls_goods_comment`，通过以下字段区分：
   - 真实评价：`user_id` 为实际用户ID，`order_goods_id` 为订单商品ID，`virtual_data` 为空
   - 虚拟评价：`user_id` 为0，`order_goods_id` 为0，`virtual_data` 不为空，`created_by_user_id` 记录创建者

2. 虚拟评价的显示与真实评价完全一致，前端无法区分

3. 为保证数据一致性，添加虚拟评价时使用了事务处理

## 前端实现建议
1. 前端需要实现以下功能：
   - 商品选择
   - 评价用户信息设置（头像、昵称、等级）
   - 评价时间选择
   - 评价等级选择（1-5星）
   - 评价内容编辑
   - 评价图片上传

2. 前端需要根据用户是否为"评论家"等级控制功能的显示与隐藏

## 安全与风险控制
1. 接口已添加权限控制，防止非"评论家"等级用户添加虚拟评价
2. 添加了每日数量限制（99条），防止恶意刷评价
3. 记录了创建者ID，方便追溯评价来源

## 后续优化方向
1. 可以考虑增加虚拟评价模板功能，方便快速添加常用评价
2. 可以增加批量添加功能，提高操作效率
3. 可以增加评价管理功能，允许用户管理自己添加的虚拟评价 