# 动态参数模板系统文档

## 系统概述

动态参数模板系统是一个集成到LikeShop项目中的海报生成系统，允许管理员配置模板参数，用户填写参数后生成个性化海报。

### 核心功能

- **模板管理**: 从迅排设计获取模板，解析文本元素，配置动态参数
- **参数配置**: 灵活的参数类型支持（文本、多行文本、邮箱、手机号等）
- **用户界面**: 动态表单生成，实时预览，图片生成
- **数据管理**: 用户数据存储，草稿保存，历史记录
- **API集成**: 与迅排设计API无缝集成

## 系统架构

### 技术栈
- **后端框架**: ThinkPHP 5.1
- **前端框架**: LayUI
- **数据库**: MySQL 5.7+
- **外部API**: 迅排设计API

### 目录结构
```
├── application/
│   ├── admin/controller/PosterTemplate.php     # 后台管理控制器
│   ├── api/controller/PosterExternal.php       # 外部API控制器
│   ├── index/controller/PosterTemplate.php     # 用户端控制器
│   ├── common/model/                           # 数据模型
│   │   ├── PosterTemplateConfig.php
│   │   ├── PosterUserData.php
│   │   └── PosterGenerationRecord.php
│   └── common/service/                         # 业务服务
│       ├── PosterTemplateService.php
│       ├── PosterUserDataService.php
│       └── PosterApiClient.php
├── database/migrations/                        # 数据库迁移文件
├── test/poster_template/                       # 测试框架
├── config/poster_template.php                 # 系统配置
├── route/poster_template.php                  # 路由配置
└── docs/                                      # 文档目录
```

## 数据库设计

### 核心表结构

#### 1. 模板配置表 (ls_poster_template_configs)
存储模板参数配置信息
- `id`: 配置ID
- `template_id`: 迅排设计模板ID
- `config_name`: 配置名称
- `parameters`: 参数定义JSON
- `status`: 启用状态

#### 2. 用户数据表 (ls_poster_user_data)
存储用户填写的参数数据
- `id`: 数据ID
- `config_id`: 配置ID
- `user_id`: 用户ID
- `parameter_values`: 参数值JSON
- `is_draft`: 是否为草稿

#### 3. 生成记录表 (ls_poster_generation_records)
存储图片生成历史记录
- `id`: 记录ID
- `data_id`: 参数数据ID
- `image_url`: 生成的图片URL
- `generation_time`: 生成耗时
- `status`: 生成状态

## API接口

### 外部API（供迅排设计调用）

#### 获取参数数据
```
GET /api/poster-external/parameter-data/{dataId}
Authorization: Bearer {api_key}
```

#### 获取参数配置
```
GET /api/poster-external/parameter-config/{configId}
Authorization: Bearer {api_key}
```

#### 批量获取数据
```
POST /api/poster-external/parameter-data/batch
Content-Type: application/json
Authorization: Bearer {api_key}

{
    "dataIds": ["data_001", "data_002"]
}
```

### 内部API（供前端调用）

#### 保存用户数据
```
POST /poster/api/save-data
Content-Type: application/json

{
    "config_id": "config_001",
    "parameter_values": {
        "greeting": "你好",
        "content": "测试内容"
    },
    "is_draft": true
}
```

#### 生成预览
```
POST /poster/api/generate-preview
Content-Type: application/json

{
    "data_id": "data_001"
}
```

#### 生成图片
```
POST /poster/api/generate-image
Content-Type: application/json

{
    "data_id": "data_001",
    "width": 1242,
    "height": 2208,
    "quality": 0.9
}
```

## 配置说明

### 环境变量配置
复制 `.env.poster_template.example` 为 `.env.poster_template` 并修改配置：

```bash
# 迅排设计API配置
POSTER_API_BASE_URL=http://localhost:7001
POSTER_API_TIMEOUT=30

# 外部API配置
POSTER_EXTERNAL_API_KEY=your-secure-api-key-here
POSTER_EXTERNAL_API_BASE_URL=http://localhost:8000

# 缓存配置
POSTER_CACHE_ENABLED=true
POSTER_TEMPLATE_CACHE_TTL=600

# 图片生成配置
POSTER_MAX_WIDTH=4000
POSTER_MAX_HEIGHT=4000
POSTER_DEFAULT_QUALITY=0.9
```

### 权限配置
系统集成了完整的权限控制：
- 后台管理权限通过admin角色控制
- 外部API通过API Key认证
- 用户数据访问通过用户身份验证

## 安装部署

### 1. 自动安装
```bash
php install_poster_template_system.php
```

### 2. 手动安装

#### 步骤1: 创建数据库表
```bash
mysql -u root -p your_database < database/migrations/install_poster_template_system.sql
```

#### 步骤2: 插入菜单数据
```bash
mysql -u root -p your_database < database/migrations/add_poster_template_menu.sql
```

#### 步骤3: 生成种子数据
```php
require_once 'test/poster_template/tools/seeders/PosterTemplateSeeder.php';
\test\poster_template\tools\seeders\PosterTemplateSeeder::run();
```

#### 步骤4: 配置权限
确保以下目录可写：
- `runtime/`
- `public/uploads/`
- `public/uploads/poster_template/`

## 使用指南

### 管理员使用

1. **配置模板**
   - 访问 `/admin/poster_template/config_list`
   - 点击"添加配置"选择模板
   - 配置参数类型和验证规则

2. **管理用户数据**
   - 查看用户生成的数据
   - 管理草稿和已发布内容

3. **查看统计**
   - 生成成功率统计
   - 用户使用情况分析

### 用户使用

1. **选择模板**
   - 访问 `/poster`
   - 浏览可用的模板配置

2. **填写参数**
   - 根据配置的参数类型填写内容
   - 支持保存草稿功能

3. **生成海报**
   - 先生成预览查看效果
   - 确认后生成最终图片
   - 支持自定义尺寸和质量

## 开发指南

### 扩展参数类型
在 `PosterUserDataService` 中添加新的参数类型验证：

```php
case 'custom_type':
    if (!empty($value) && !$this->validateCustomType($value)) {
        $errors[] = "参数 {$param['parameterLabel']} 格式不正确";
    }
    break;
```

### 添加新的API接口
1. 在对应的控制器中添加方法
2. 在 `route/poster_template.php` 中添加路由
3. 更新权限配置

### 自定义前端组件
在 `public/static/admin/js/poster_template.js` 中扩展功能：

```javascript
PosterTemplate.customFunction = function() {
    // 自定义功能实现
};
```

## 故障排除

### 常见问题

1. **API调用失败**
   - 检查API Key配置
   - 确认网络连接
   - 查看日志文件

2. **图片生成失败**
   - 检查迅排设计API状态
   - 确认参数数据完整性
   - 查看生成记录表的错误信息

3. **权限问题**
   - 确认用户角色权限
   - 检查中间件配置
   - 查看权限表数据

### 日志查看
系统日志位置：
- 应用日志: `runtime/log/`
- API调用日志: 搜索 "Poster API" 关键词
- 错误日志: 搜索 "Poster template" 关键词

## 性能优化

### 缓存策略
- 模板数据缓存（10分钟）
- 解析结果缓存（1小时）
- 用户数据缓存（5分钟）

### 数据库优化
- 已添加必要的索引
- 支持分区表（大数据量场景）
- 定期清理过期记录

### 监控建议
- 监控API响应时间
- 跟踪生成成功率
- 监控数据库性能

## 版本更新

### 当前版本: 1.0.0
- 基础功能完整实现
- 支持多种参数类型
- 完整的权限控制
- 用户友好的界面

### 后续规划
- 支持更多参数类型
- 批量生成功能
- 模板分享功能
- 移动端适配

## 技术支持

如有问题，请查看：
1. 系统日志文件
2. 数据库错误记录
3. API调用记录

或联系开发团队获取支持。
