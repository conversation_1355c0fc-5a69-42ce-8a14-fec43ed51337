<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个性化海报制作</title>
    <link rel="stylesheet" href="/static/layui/css/layui.css">
    <style>
        body { background: #f5f5f5; }
        .header { background: white; padding: 20px 0; margin-bottom: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
        .header h1 { margin: 0; color: #333; font-size: 28px; }
        .header p { margin: 10px 0 0 0; color: #666; }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
        .config-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); gap: 20px; }
        .config-card { background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 12px rgba(0,0,0,0.1); transition: transform 0.3s, box-shadow 0.3s; }
        .config-card:hover { transform: translateY(-5px); box-shadow: 0 8px 25px rgba(0,0,0,0.15); }
        .config-header { padding: 20px; border-bottom: 1px solid #f0f0f0; }
        .config-title { margin: 0 0 8px 0; font-size: 18px; font-weight: bold; color: #333; }
        .config-description { margin: 0; color: #666; font-size: 14px; line-height: 1.5; }
        .config-meta { padding: 15px 20px; background: #fafafa; }
        .config-meta-item { display: inline-block; margin-right: 15px; font-size: 12px; color: #999; }
        .config-meta-item i { margin-right: 4px; }
        .config-actions { padding: 20px; text-align: center; }
        .config-actions .layui-btn { width: 100%; }
        .stats-section { background: white; border-radius: 12px; padding: 30px; margin-bottom: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; }
        .stat-item { text-align: center; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 8px; }
        .stat-number { font-size: 32px; font-weight: bold; margin-bottom: 5px; }
        .stat-label { font-size: 14px; opacity: 0.9; }
        .empty-state { text-align: center; padding: 60px 20px; color: #999; }
        .empty-state i { font-size: 64px; margin-bottom: 20px; display: block; }
    </style>
</head>
<body>
    <!-- 头部 -->
    <div class="header">
        <div class="container">
            <h1>个性化海报制作</h1>
            <p>选择模板，填写参数，一键生成专属海报</p>
        </div>
    </div>
    
    <div class="container">
        <!-- 统计信息 -->
        <div class="stats-section">
            <h2 style="margin: 0 0 20px 0; color: #333;">平台统计</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number" id="total-configs">0</div>
                    <div class="stat-label">可用模板</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="total-generations">0</div>
                    <div class="stat-label">生成次数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="success-rate">0%</div>
                    <div class="stat-label">成功率</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="avg-time">0s</div>
                    <div class="stat-label">平均耗时</div>
                </div>
            </div>
        </div>
        
        <!-- 模板配置列表 -->
        <div class="layui-card">
            <div class="layui-card-header">
                <h2 style="margin: 0; color: #333;">选择模板</h2>
            </div>
            <div class="layui-card-body">
                {if $configList}
                <div class="config-grid">
                    {volist name="configList" id="config"}
                    <div class="config-card">
                        <div class="config-header">
                            <h3 class="config-title">{$config.config_name}</h3>
                            <p class="config-description">{$config.config_description|default='暂无描述'}</p>
                        </div>
                        <div class="config-meta">
                            <span class="config-meta-item">
                                <i class="layui-icon layui-icon-template-1"></i>
                                模板: {$config.template_title|default=$config.template_id}
                            </span>
                            <span class="config-meta-item">
                                <i class="layui-icon layui-icon-set"></i>
                                参数: {$config.parameter_stats.total}个
                            </span>
                            <span class="config-meta-item">
                                <i class="layui-icon layui-icon-user"></i>
                                使用: {$config.user_data_count}次
                            </span>
                        </div>
                        <div class="config-actions">
                            <a href="/index/poster_template/form/config_id/{$config.id}" class="layui-btn layui-btn-fluid">
                                <i class="layui-icon layui-icon-add-1"></i>
                                开始制作
                            </a>
                        </div>
                    </div>
                    {/volist}
                </div>
                {else}
                <div class="empty-state">
                    <i class="layui-icon layui-icon-template-1"></i>
                    <h3>暂无可用模板</h3>
                    <p>管理员还没有配置任何模板，请稍后再试</p>
                </div>
                {/if}
            </div>
        </div>
        
        <!-- 我的作品入口 -->
        <div style="text-align: center; margin: 40px 0;">
            <a href="/index/poster_template/my_works" class="layui-btn layui-btn-lg layui-btn-normal">
                <i class="layui-icon layui-icon-picture"></i>
                查看我的作品
            </a>
        </div>
    </div>

    <script src="/static/layui/layui.js"></script>
    <script>
        layui.use(['layer'], function(){
            var layer = layui.layer;
            
            // 加载统计数据
            loadStats();
            
            function loadStats() {
                $.get('/admin/poster_template/get_stats', function(res) {
                    if (res.code == 1) {
                        var stats = res.data;
                        document.getElementById('total-configs').textContent = stats.total_configs || 0;
                        document.getElementById('total-generations').textContent = stats.total_generations || 0;
                        document.getElementById('success-rate').textContent = (stats.success_rate || 0) + '%';
                        document.getElementById('avg-time').textContent = (stats.avg_generation_time || 0) + 's';
                    }
                }).fail(function() {
                    // 静默失败，不影响页面显示
                });
            }
            
            // 添加卡片点击效果
            $('.config-card').on('click', function(e) {
                if (!$(e.target).hasClass('layui-btn') && !$(e.target).closest('.layui-btn').length) {
                    var link = $(this).find('.layui-btn').attr('href');
                    if (link) {
                        window.location.href = link;
                    }
                }
            });
        });
    </script>
</body>
</html>
