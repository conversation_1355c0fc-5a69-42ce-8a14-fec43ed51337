<?php
/**
 * 动态参数模板系统 - 图片生成记录模型
 */

namespace app\common\model;

use think\Model;

class PosterGenerationRecord extends Model
{
    // 表名
    protected $table = 'ls_poster_generation_records';
    
    // 主键
    protected $pk = 'id';
    
    // 自动时间戳
    protected $autoWriteTimestamp = 'created_at';
    protected $createTime = 'created_at';
    protected $updateTime = false; // 生成记录不需要更新时间
    
    // JSON字段
    protected $json = ['generation_options'];
    protected $jsonAssoc = true;
    
    // 类型转换
    protected $type = [
        'generation_time' => 'float',
        'file_size' => 'integer',
        'image_width' => 'integer',
        'image_height' => 'integer',
        'quality' => 'float',
        'status' => 'boolean',
    ];
    
    // 状态常量
    const STATUS_FAILED = 0;  // 失败
    const STATUS_SUCCESS = 1; // 成功
    
    /**
     * 获取状态描述
     * @param int|null $status
     * @return array|string
     */
    public static function getStatusDesc($status = null)
    {
        $data = [
            self::STATUS_FAILED => '失败',
            self::STATUS_SUCCESS => '成功',
        ];
        
        if ($status === null) {
            return $data;
        }
        
        return $data[$status] ?? '未知';
    }
    
    /**
     * 状态获取器
     * @param $value
     * @return string
     */
    public function getStatusTextAttr($value, $data)
    {
        return self::getStatusDesc($data['status']);
    }
    
    /**
     * 文件大小获取器 - 转换为可读格式
     * @param $value
     * @return string
     */
    public function getFileSizeTextAttr($value, $data)
    {
        $size = $data['file_size'] ?? 0;
        if ($size < 1024) {
            return $size . ' B';
        } elseif ($size < 1048576) {
            return round($size / 1024, 2) . ' KB';
        } else {
            return round($size / 1048576, 2) . ' MB';
        }
    }
    
    /**
     * 生成时间获取器 - 转换为可读格式
     * @param $value
     * @return string
     */
    public function getGenerationTimeTextAttr($value, $data)
    {
        $time = $data['generation_time'] ?? 0;
        if ($time < 1) {
            return round($time * 1000) . ' ms';
        } else {
            return round($time, 2) . ' s';
        }
    }
    
    /**
     * 生成选项获取器 - 确保返回数组
     * @param $value
     * @return array
     */
    public function getGenerationOptionsAttr($value)
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return $decoded ?: [];
        }
        return $value ?: [];
    }
    
    /**
     * 生成选项设置器 - 确保存储为JSON
     * @param $value
     * @return string
     */
    public function setGenerationOptionsAttr($value)
    {
        return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }
    
    /**
     * 关联用户数据
     * @return \think\model\relation\BelongsTo
     */
    public function userData()
    {
        return $this->belongsTo(PosterUserData::class, 'data_id', 'id');
    }
    
    /**
     * 创建生成记录
     * @param array $data
     * @return PosterGenerationRecord|false
     */
    public static function createRecord($data)
    {
        // 生成唯一ID
        if (empty($data['id'])) {
            $data['id'] = self::generateId();
        }
        
        // 验证必填字段
        if (empty($data['data_id']) || empty($data['image_url'])) {
            return false;
        }
        
        // 设置默认状态
        if (!isset($data['status'])) {
            $data['status'] = self::STATUS_SUCCESS;
        }
        
        return self::create($data);
    }
    
    /**
     * 记录成功的生成
     * @param string $dataId
     * @param string $imageUrl
     * @param array $options
     * @param float $generationTime
     * @param int $fileSize
     * @return PosterGenerationRecord|false
     */
    public static function recordSuccess($dataId, $imageUrl, $options = [], $generationTime = null, $fileSize = null)
    {
        $data = [
            'data_id' => $dataId,
            'image_url' => $imageUrl,
            'generation_options' => $options,
            'generation_time' => $generationTime,
            'file_size' => $fileSize,
            'status' => self::STATUS_SUCCESS,
        ];
        
        // 从选项中提取图片信息
        if (!empty($options['width'])) {
            $data['image_width'] = $options['width'];
        }
        if (!empty($options['height'])) {
            $data['image_height'] = $options['height'];
        }
        if (!empty($options['quality'])) {
            $data['quality'] = $options['quality'];
        }
        if (!empty($options['format'])) {
            $data['image_format'] = $options['format'];
        }
        
        return self::createRecord($data);
    }
    
    /**
     * 记录失败的生成
     * @param string $dataId
     * @param string $errorMessage
     * @param array $options
     * @param float $generationTime
     * @return PosterGenerationRecord|false
     */
    public static function recordFailure($dataId, $errorMessage, $options = [], $generationTime = null)
    {
        $data = [
            'data_id' => $dataId,
            'image_url' => '',
            'generation_options' => $options,
            'generation_time' => $generationTime,
            'status' => self::STATUS_FAILED,
            'error_message' => $errorMessage,
        ];
        
        return self::createRecord($data);
    }
    
    /**
     * 获取数据的生成记录
     * @param string $dataId
     * @param string $order
     * @return \think\Collection
     */
    public static function getDataRecords($dataId, $order = 'created_at desc')
    {
        return self::where('data_id', $dataId)->order($order)->select();
    }
    
    /**
     * 获取成功的生成记录
     * @param array $where
     * @param string $order
     * @return \think\Collection
     */
    public static function getSuccessRecords($where = [], $order = 'created_at desc')
    {
        $where['status'] = self::STATUS_SUCCESS;
        return self::where($where)->order($order)->select();
    }
    
    /**
     * 获取失败的生成记录
     * @param array $where
     * @param string $order
     * @return \think\Collection
     */
    public static function getFailedRecords($where = [], $order = 'created_at desc')
    {
        $where['status'] = self::STATUS_FAILED;
        return self::where($where)->order($order)->select();
    }
    
    /**
     * 生成唯一ID
     * @return string
     */
    public static function generateId()
    {
        return 'record_' . date('YmdHis') . '_' . uniqid();
    }
    
    /**
     * 获取生成统计
     * @param array $where
     * @return array
     */
    public static function getGenerationStats($where = [])
    {
        $total = self::where($where)->count();
        $success = self::where($where)->where('status', self::STATUS_SUCCESS)->count();
        $failed = self::where($where)->where('status', self::STATUS_FAILED)->count();
        
        // 平均生成时间
        $avgTime = self::where($where)
            ->where('status', self::STATUS_SUCCESS)
            ->where('generation_time', '>', 0)
            ->avg('generation_time');
        
        // 平均文件大小
        $avgSize = self::where($where)
            ->where('status', self::STATUS_SUCCESS)
            ->where('file_size', '>', 0)
            ->avg('file_size');
        
        return [
            'total' => $total,
            'success' => $success,
            'failed' => $failed,
            'success_rate' => $total > 0 ? round($success / $total * 100, 2) : 0,
            'avg_generation_time' => round($avgTime ?: 0, 3),
            'avg_file_size' => round($avgSize ?: 0),
        ];
    }
    
    /**
     * 获取热门尺寸统计
     * @param int $limit
     * @return array
     */
    public static function getPopularSizes($limit = 10)
    {
        return self::where('status', self::STATUS_SUCCESS)
            ->where('image_width', '>', 0)
            ->where('image_height', '>', 0)
            ->field('image_width, image_height, COUNT(*) as count')
            ->group('image_width, image_height')
            ->order('count desc')
            ->limit($limit)
            ->select()
            ->toArray();
    }
    
    /**
     * 清理过期记录
     * @param int $days 保留天数
     * @return int 删除的记录数
     */
    public static function cleanExpiredRecords($days = 30)
    {
        $expiredDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        return self::where('created_at', '<', $expiredDate)->delete();
    }
}
