<?php
/**
 * 动态参数模板系统API客户端
 * 用于调用迅排设计API服务
 */

namespace app\common\service;

use think\facade\Config;
use think\facade\Log;
use think\facade\Cache;

class PosterApiClient
{
    protected $baseUrl;
    protected $timeout;
    protected $retryTimes;
    protected $retryDelay;
    protected $cacheEnabled;
    
    public function __construct()
    {
        $config = Config::get('poster_template.poster_api');
        $this->baseUrl = $config['base_url'] ?? 'http://localhost:7001';
        $this->timeout = $config['timeout'] ?? 30;
        $this->retryTimes = $config['retry_times'] ?? 3;
        $this->retryDelay = $config['retry_delay'] ?? 1000; // 毫秒
        $this->cacheEnabled = Config::get('poster_template.cache.enabled', true);
    }
    
    /**
     * 获取模板列表
     * @param array $params
     * @return array
     */
    public function getTemplates($params = [])
    {
        $cacheKey = 'poster_templates_' . md5(serialize($params));
        
        if ($this->cacheEnabled) {
            $cached = Cache::get($cacheKey);
            if ($cached !== false) {
                return $cached;
            }
        }
        
        $url = '/api/templates';
        $response = $this->request('GET', $url, $params);
        
        if ($this->cacheEnabled && $response['success']) {
            $ttl = Config::get('poster_template.cache.template_ttl', 600);
            Cache::set($cacheKey, $response, $ttl);
        }
        
        return $response;
    }
    
    /**
     * 获取模板详情
     * @param string $templateId
     * @return array
     */
    public function getTemplate($templateId)
    {
        $cacheKey = "poster_template_{$templateId}";
        
        if ($this->cacheEnabled) {
            $cached = Cache::get($cacheKey);
            if ($cached !== false) {
                return $cached;
            }
        }
        
        $url = '/api/template';
        $response = $this->request('GET', $url, ['id' => $templateId]);
        
        if ($this->cacheEnabled && $response['success']) {
            $ttl = Config::get('poster_template.cache.template_ttl', 600);
            Cache::set($cacheKey, $response, $ttl);
        }
        
        return $response;
    }
    
    /**
     * 解析模板
     * @param string $templateId
     * @return array
     */
    public function parseTemplate($templateId)
    {
        $cacheKey = "poster_parse_{$templateId}";
        
        if ($this->cacheEnabled) {
            $cached = Cache::get($cacheKey);
            if ($cached !== false) {
                return $cached;
            }
        }
        
        $url = '/api/template/parse';
        $data = ['templateId' => $templateId];
        $response = $this->request('POST', $url, $data);
        
        if ($this->cacheEnabled && $response['success']) {
            $ttl = Config::get('poster_template.cache.parse_ttl', 3600);
            Cache::set($cacheKey, $response, $ttl);
        }
        
        return $response;
    }
    
    /**
     * 生成参数化预览
     * @param string $templateId
     * @param string $parameterDataId
     * @return array
     */
    public function generatePreview($templateId, $parameterDataId)
    {
        $url = '/api/parameter/preview';
        $data = [
            'templateId' => $templateId,
            'parameterDataId' => $parameterDataId
        ];
        
        return $this->request('POST', $url, $data);
    }
    
    /**
     * 执行参数替换
     * @param string $templateId
     * @param string $parameterDataId
     * @return array
     */
    public function replaceParameters($templateId, $parameterDataId)
    {
        $url = '/api/parameter/replace';
        $data = [
            'templateId' => $templateId,
            'parameterDataId' => $parameterDataId
        ];
        
        return $this->request('POST', $url, $data);
    }
    
    /**
     * 生成图片
     * @param array $params
     * @return array
     */
    public function generateImage($params)
    {
        $url = '/api/screenshots';
        return $this->request('GET', $url, $params);
    }
    
    /**
     * 批量生成图片
     * @param array $dataIds
     * @param array $outputOptions
     * @return array
     */
    public function batchGenerate($dataIds, $outputOptions)
    {
        $url = '/api/parameter/batch-generate';
        $data = [
            'dataIds' => $dataIds,
            'outputOptions' => $outputOptions
        ];
        
        return $this->request('POST', $url, $data);
    }
    
    /**
     * 查询批量任务状态
     * @param string $batchId
     * @return array
     */
    public function getBatchStatus($batchId)
    {
        $url = "/api/parameter/batch-status/{$batchId}";
        return $this->request('GET', $url);
    }
    
    /**
     * 健康检查
     * @return array
     */
    public function healthCheck()
    {
        $url = '/health';
        return $this->request('GET', $url);
    }
    
    /**
     * 发送HTTP请求
     * @param string $method
     * @param string $url
     * @param array $data
     * @return array
     */
    protected function request($method, $url, $data = [])
    {
        $fullUrl = rtrim($this->baseUrl, '/') . $url;
        $attempt = 0;
        $lastError = null;
        
        while ($attempt < $this->retryTimes) {
            $attempt++;
            
            try {
                $startTime = microtime(true);
                $response = $this->makeHttpRequest($method, $fullUrl, $data);
                $duration = microtime(true) - $startTime;
                
                // 记录成功的API调用
                Log::info('Poster API request successful', [
                    'method' => $method,
                    'url' => $url,
                    'attempt' => $attempt,
                    'duration' => round($duration, 3)
                ]);
                
                return [
                    'success' => true,
                    'data' => $response,
                    'duration' => $duration
                ];
                
            } catch (\Exception $e) {
                $lastError = $e;
                
                Log::warning('Poster API request failed', [
                    'method' => $method,
                    'url' => $url,
                    'attempt' => $attempt,
                    'error' => $e->getMessage()
                ]);
                
                // 如果不是最后一次尝试，等待后重试
                if ($attempt < $this->retryTimes) {
                    usleep($this->retryDelay * 1000); // 转换为微秒
                }
            }
        }
        
        // 所有重试都失败了
        Log::error('Poster API request failed after all retries', [
            'method' => $method,
            'url' => $url,
            'attempts' => $attempt,
            'error' => $lastError->getMessage()
        ]);
        
        return [
            'success' => false,
            'error' => $lastError->getMessage(),
            'attempts' => $attempt
        ];
    }
    
    /**
     * 执行HTTP请求
     * @param string $method
     * @param string $url
     * @param array $data
     * @return array
     * @throws \Exception
     */
    protected function makeHttpRequest($method, $url, $data = [])
    {
        // 使用ThinkPHP内置的HTTP客户端或者Requests库
        $options = [
            'timeout' => $this->timeout,
            'headers' => [
                'Content-Type' => 'application/json',
                'User-Agent' => 'LikeShop-PosterTemplate/1.0'
            ]
        ];
        
        if ($method === 'GET' && !empty($data)) {
            $url .= '?' . http_build_query($data);
            $data = [];
        }
        
        if (!empty($data)) {
            $options['data'] = json_encode($data);
        }
        
        // 使用Requests库发送请求
        if (class_exists('\Requests')) {
            $response = \Requests::request($url, $options['headers'], $options['data'] ?? [], $method, $options);
            
            if (!$response->success) {
                throw new \Exception("HTTP request failed with status {$response->status_code}");
            }
            
            $responseData = json_decode($response->body, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception('Invalid JSON response');
            }
            
            if (isset($responseData['code']) && $responseData['code'] !== 200) {
                throw new \Exception($responseData['message'] ?? 'API error');
            }
            
            return $responseData;
        }
        
        // 备用方案：使用cURL
        return $this->curlRequest($method, $url, $data, $options);
    }
    
    /**
     * 使用cURL发送请求
     * @param string $method
     * @param string $url
     * @param array $data
     * @param array $options
     * @return array
     * @throws \Exception
     */
    protected function curlRequest($method, $url, $data, $options)
    {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $options['timeout'],
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'User-Agent: LikeShop-PosterTemplate/1.0'
            ],
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);
        
        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            if (!empty($data)) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        } elseif ($method === 'PUT') {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
            if (!empty($data)) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new \Exception("cURL error: {$error}");
        }
        
        if ($httpCode >= 400) {
            throw new \Exception("HTTP error: {$httpCode}");
        }
        
        $responseData = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception('Invalid JSON response');
        }
        
        if (isset($responseData['code']) && $responseData['code'] !== 200) {
            throw new \Exception($responseData['message'] ?? 'API error');
        }
        
        return $responseData;
    }
    
    /**
     * 清除缓存
     * @param string|null $pattern
     */
    public function clearCache($pattern = null)
    {
        if ($pattern) {
            Cache::clear($pattern);
        } else {
            // 清除所有poster相关缓存
            Cache::clear('poster_');
        }
    }
}
