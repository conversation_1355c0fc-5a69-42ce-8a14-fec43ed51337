-- 动态参数模板系统种子数据
-- 用于开发和测试环境的初始数据

SET NAMES utf8mb4;

-- ============================================================================
-- 1. 模板配置种子数据
-- ============================================================================

INSERT INTO `ls_poster_template_configs` (`id`, `template_id`, `template_title`, `config_name`, `config_description`, `parameters`, `created_by`, `created_at`, `updated_at`, `status`) VALUES
('config_demo_001', '1', '日签海报模板', '个性化日签配置', '用于生成个性化日签的参数配置', 
JSON_OBJECT(
    'parameters', JSON_ARRAY(
        JSON_OBJECT(
            'id', 'param_001',
            'elementUuid', 'uuid_greeting',
            'parameterName', 'greeting',
            'parameterLabel', '问候语',
            'parameterType', 'text',
            'isRequired', true,
            'defaultValue', '你好，世界',
            'validationRules', JSON_OBJECT('maxLength', 50),
            'displayOrder', 1,
            'isEnabled', true,
            'parameterDescription', '显示在海报顶部的问候语'
        ),
        JSON_OBJECT(
            'id', 'param_002',
            'elementUuid', 'uuid_quote',
            'parameterName', 'daily_quote',
            'parameterLabel', '每日金句',
            'parameterType', 'textarea',
            'isRequired', true,
            'defaultValue', '成功不是终点，失败不是致命的，重要的是继续前进的勇气。',
            'validationRules', JSON_OBJECT('maxLength', 200),
            'displayOrder', 2,
            'isEnabled', true,
            'parameterDescription', '显示在海报中央的励志金句'
        ),
        JSON_OBJECT(
            'id', 'param_003',
            'elementUuid', 'uuid_date',
            'parameterName', 'display_date',
            'parameterLabel', '显示日期',
            'parameterType', 'text',
            'isRequired', false,
            'defaultValue', '2025年1月16日',
            'validationRules', JSON_OBJECT('maxLength', 20),
            'displayOrder', 3,
            'isEnabled', true,
            'parameterDescription', '显示在海报底部的日期'
        )
    )
), 'admin', NOW(), NOW(), 1),

('config_demo_002', '2', '商品宣传海报', '商品推广配置', '用于商品宣传推广的参数配置', 
JSON_OBJECT(
    'parameters', JSON_ARRAY(
        JSON_OBJECT(
            'id', 'param_004',
            'elementUuid', 'uuid_product_name',
            'parameterName', 'product_name',
            'parameterLabel', '商品名称',
            'parameterType', 'text',
            'isRequired', true,
            'defaultValue', '精选好物',
            'validationRules', JSON_OBJECT('maxLength', 100),
            'displayOrder', 1,
            'isEnabled', true,
            'parameterDescription', '商品的名称标题'
        ),
        JSON_OBJECT(
            'id', 'param_005',
            'elementUuid', 'uuid_price',
            'parameterName', 'price',
            'parameterLabel', '商品价格',
            'parameterType', 'text',
            'isRequired', true,
            'defaultValue', '¥99.00',
            'validationRules', JSON_OBJECT('maxLength', 20),
            'displayOrder', 2,
            'isEnabled', true,
            'parameterDescription', '商品的销售价格'
        ),
        JSON_OBJECT(
            'id', 'param_006',
            'elementUuid', 'uuid_description',
            'parameterName', 'description',
            'parameterLabel', '商品描述',
            'parameterType', 'textarea',
            'isRequired', false,
            'defaultValue', '品质保证，值得信赖',
            'validationRules', JSON_OBJECT('maxLength', 150),
            'displayOrder', 3,
            'isEnabled', true,
            'parameterDescription', '商品的详细描述'
        ),
        JSON_OBJECT(
            'id', 'param_007',
            'elementUuid', 'uuid_contact',
            'parameterName', 'contact_info',
            'parameterLabel', '联系方式',
            'parameterType', 'phone',
            'isRequired', false,
            'defaultValue', '138-0000-0000',
            'validationRules', JSON_OBJECT('maxLength', 20),
            'displayOrder', 4,
            'isEnabled', true,
            'parameterDescription', '商家联系电话'
        )
    )
), 'admin', NOW(), NOW(), 1);

-- ============================================================================
-- 2. 用户数据种子数据
-- ============================================================================

INSERT INTO `ls_poster_user_data` (`id`, `config_id`, `user_id`, `session_id`, `parameter_values`, `is_draft`, `preview_url`, `generated_image_url`, `created_at`, `updated_at`) VALUES
('data_demo_001', 'config_demo_001', 'user_001', NULL, 
JSON_OBJECT(
    'greeting', '你好，新年快乐',
    'daily_quote', '新年新气象，愿所有美好如期而至，愿所有努力都有收获！',
    'display_date', '2025年1月16日'
), 0, 'http://localhost:7001/preview/parameter/data_demo_001', 'http://localhost:7001/static/generated/data_demo_001.jpg', NOW(), NOW()),

('data_demo_002', 'config_demo_001', NULL, 'session_001', 
JSON_OBJECT(
    'greeting', '早安，世界',
    'daily_quote', '每一个清晨都是新的开始，带着希望迎接美好的一天。',
    'display_date', '2025年1月16日'
), 1, NULL, NULL, NOW(), NOW()),

('data_demo_003', 'config_demo_002', 'user_002', NULL, 
JSON_OBJECT(
    'product_name', '超值优选商品套装',
    'price', '¥199.00',
    'description', '精心挑选的优质商品，品质保证，限时特惠！',
    'contact_info', '138-8888-8888'
), 0, 'http://localhost:7001/preview/parameter/data_demo_003', 'http://localhost:7001/static/generated/data_demo_003.jpg', NOW(), NOW());

-- ============================================================================
-- 3. 生成记录种子数据
-- ============================================================================

INSERT INTO `ls_poster_generation_records` (`id`, `data_id`, `image_url`, `generation_options`, `generation_time`, `file_size`, `image_width`, `image_height`, `image_format`, `quality`, `status`, `error_message`, `created_at`) VALUES
('record_demo_001', 'data_demo_001', 'http://localhost:7001/static/generated/data_demo_001.jpg', 
JSON_OBJECT(
    'width', 1242,
    'height', 2208,
    'quality', 0.9,
    'format', 'jpg'
), 2.345, 856432, 1242, 2208, 'jpg', 0.90, 1, NULL, NOW()),

('record_demo_002', 'data_demo_003', 'http://localhost:7001/static/generated/data_demo_003.jpg', 
JSON_OBJECT(
    'width', 800,
    'height', 1200,
    'quality', 0.8,
    'format', 'jpg'
), 1.876, 654321, 800, 1200, 'jpg', 0.80, 1, NULL, NOW()),

('record_demo_003', 'data_demo_001', '', 
JSON_OBJECT(
    'width', 1242,
    'height', 2208,
    'quality', 0.9,
    'format', 'png'
), 5.123, 0, 0, 0, '', 0.00, 0, '生成超时：API响应时间过长', NOW());

-- 显示插入结果
SELECT 'Poster Template System Seed Data Inserted Successfully!' as Status;
SELECT 
    (SELECT COUNT(*) FROM ls_poster_template_configs) as template_configs,
    (SELECT COUNT(*) FROM ls_poster_user_data) as user_data,
    (SELECT COUNT(*) FROM ls_poster_generation_records) as generation_records;
