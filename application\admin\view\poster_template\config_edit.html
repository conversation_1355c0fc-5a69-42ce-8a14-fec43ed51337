{layout name="layout1" /}
<div class="layui-fluid">
<div class="layui-card">
    <div class="layui-card-header">
        <span>编辑模板配置</span>
        <div class="layui-btn-group fr">
            <a class="layui-btn layui-btn-primary layui-btn-sm" href="{:url('poster_template/config_list')}">
                <i class="layui-icon layui-icon-return"></i>返回列表
            </a>
        </div>
    </div>
    <div class="layui-card-body">
        <form class="layui-form" lay-filter="config-form">
            <input type="hidden" name="id" value="{$config.id}">
            
            <div class="layui-row layui-col-space20">
                <!-- 左侧：基本信息 -->
                <div class="layui-col-md4">
                    <div class="layui-card">
                        <div class="layui-card-header">基本信息</div>
                        <div class="layui-card-body">
                            <div class="layui-form-item">
                                <label class="layui-form-label">模板ID</label>
                                <div class="layui-input-block">
                                    <input type="text" value="{$config.template_id}" readonly class="layui-input layui-disabled">
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">模板标题</label>
                                <div class="layui-input-block">
                                    <input type="text" value="{$config.template_title}" readonly class="layui-input layui-disabled">
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">配置名称</label>
                                <div class="layui-input-block">
                                    <input type="text" name="config_name" value="{$config.config_name}" required lay-verify="required" class="layui-input">
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">配置描述</label>
                                <div class="layui-input-block">
                                    <textarea name="config_description" class="layui-textarea">{$config.config_description}</textarea>
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">状态</label>
                                <div class="layui-input-block">
                                    <input type="radio" name="status" value="1" title="启用" {if $config.status == 1}checked{/if}>
                                    <input type="radio" name="status" value="0" title="禁用" {if $config.status == 0}checked{/if}>
                                </div>
                            </div>
                            
                            <!-- 统计信息 -->
                            <div class="layui-form-item">
                                <label class="layui-form-label">统计信息</label>
                                <div class="layui-input-block">
                                    <div class="stats-info">
                                        <p><span class="layui-badge layui-bg-blue">总参数: {$config.parameter_stats.total}</span></p>
                                        <p><span class="layui-badge layui-bg-green">启用: {$config.parameter_stats.enabled}</span></p>
                                        <p><span class="layui-badge layui-bg-orange">必填: {$config.parameter_stats.required}</span></p>
                                        <p><span class="layui-badge layui-bg-gray">用户数据: {$config.user_data_stats.total}</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧：参数配置 -->
                <div class="layui-col-md8">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            参数配置
                            <div class="layui-btn-group fr">
                                <button type="button" class="layui-btn layui-btn-sm" id="add-parameter">
                                    <i class="layui-icon layui-icon-add-1"></i>添加参数
                                </button>
                                <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="sort-parameters">
                                    <i class="layui-icon layui-icon-up-down"></i>排序
                                </button>
                            </div>
                        </div>
                        <div class="layui-card-body">
                            <div id="parameters-container">
                                <!-- 参数列表将在这里动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="submit">
                        <i class="layui-icon layui-icon-ok"></i>保存配置
                    </button>
                    <button type="button" class="layui-btn layui-btn-normal" id="preview-config">
                        <i class="layui-icon layui-icon-survey"></i>预览配置
                    </button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 参数配置模板 -->
<script type="text/html" id="parameter-item-tpl">
    <div class="parameter-item" data-id="{{d.id}}">
        <div class="parameter-header">
            <div class="parameter-title">
                <i class="layui-icon layui-icon-slider"></i>
                <span class="parameter-name">{{d.parameterLabel || d.parameterName}}</span>
                <span class="parameter-type layui-badge layui-bg-blue">{{d.parameterType}}</span>
                {{# if(d.isRequired) { }}
                <span class="layui-badge layui-bg-red">必填</span>
                {{# } }}
                {{# if(!d.isEnabled) { }}
                <span class="layui-badge">禁用</span>
                {{# } }}
            </div>
            <div class="parameter-actions">
                <button type="button" class="layui-btn layui-btn-xs" onclick="toggleParameter(this)">
                    <i class="layui-icon layui-icon-down"></i>
                </button>
                <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="removeParameter(this)">
                    <i class="layui-icon layui-icon-delete"></i>
                </button>
            </div>
        </div>
        <div class="parameter-content" style="display: none;">
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">参数名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="parameters[{{d.id}}][parameterName]" value="{{d.parameterName}}" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">显示标签</label>
                        <div class="layui-input-block">
                            <input type="text" name="parameters[{{d.id}}][parameterLabel]" value="{{d.parameterLabel}}" class="layui-input">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md4">
                    <div class="layui-form-item">
                        <label class="layui-form-label">参数类型</label>
                        <div class="layui-input-block">
                            <select name="parameters[{{d.id}}][parameterType]">
                                <option value="text" {{d.parameterType == 'text' ? 'selected' : ''}}>文本</option>
                                <option value="textarea" {{d.parameterType == 'textarea' ? 'selected' : ''}}>多行文本</option>
                                <option value="email" {{d.parameterType == 'email' ? 'selected' : ''}}>邮箱</option>
                                <option value="phone" {{d.parameterType == 'phone' ? 'selected' : ''}}>手机号</option>
                                <option value="number" {{d.parameterType == 'number' ? 'selected' : ''}}>数字</option>
                                <option value="date" {{d.parameterType == 'date' ? 'selected' : ''}}>日期</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md4">
                    <div class="layui-form-item">
                        <label class="layui-form-label">显示顺序</label>
                        <div class="layui-input-block">
                            <input type="number" name="parameters[{{d.id}}][displayOrder]" value="{{d.displayOrder}}" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-col-md4">
                    <div class="layui-form-item">
                        <label class="layui-form-label">最大长度</label>
                        <div class="layui-input-block">
                            <input type="number" name="parameters[{{d.id}}][maxLength]" value="{{d.validationRules.maxLength || ''}}" class="layui-input">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">默认值</label>
                <div class="layui-input-block">
                    <input type="text" name="parameters[{{d.id}}][defaultValue]" value="{{d.defaultValue}}" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">参数描述</label>
                <div class="layui-input-block">
                    <textarea name="parameters[{{d.id}}][parameterDescription]" class="layui-textarea">{{d.parameterDescription}}</textarea>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <input type="checkbox" name="parameters[{{d.id}}][isRequired]" value="1" title="必填项" {{d.isRequired ? 'checked' : ''}}>
                    <input type="checkbox" name="parameters[{{d.id}}][isEnabled]" value="1" title="启用" {{d.isEnabled ? 'checked' : ''}}>
                </div>
            </div>
            
            <input type="hidden" name="parameters[{{d.id}}][id]" value="{{d.id}}">
            <input type="hidden" name="parameters[{{d.id}}][elementUuid]" value="{{d.elementUuid}}">
        </div>
    </div>
</script>

{/block}

{block name="script"}
<script>
layui.use(['form', 'layer', 'laytpl', 'sortable'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var laytpl = layui.laytpl;
    
    var configData = {$config|json_encode};
    var parameters = configData.parameters || [];
    
    // 渲染参数列表
    function renderParameters() {
        var container = document.getElementById('parameters-container');
        var getTpl = document.getElementById('parameter-item-tpl').innerHTML;
        
        container.innerHTML = '';
        
        parameters.forEach(function(param, index) {
            laytpl(getTpl).render(param, function(html) {
                container.innerHTML += html;
            });
        });
        
        form.render();
    }
    
    // 切换参数展开/收起
    window.toggleParameter = function(btn) {
        var $btn = $(btn);
        var $content = $btn.closest('.parameter-item').find('.parameter-content');
        var $icon = $btn.find('i');
        
        if ($content.is(':visible')) {
            $content.slideUp();
            $icon.removeClass('layui-icon-up').addClass('layui-icon-down');
        } else {
            $content.slideDown();
            $icon.removeClass('layui-icon-down').addClass('layui-icon-up');
        }
    };
    
    // 删除参数
    window.removeParameter = function(btn) {
        layer.confirm('确定要删除此参数吗？', function(index) {
            var $item = $(btn).closest('.parameter-item');
            var paramId = $item.data('id');
            
            // 从数组中移除
            parameters = parameters.filter(function(param) {
                return param.id !== paramId;
            });
            
            $item.remove();
            layer.close(index);
        });
    };
    
    // 添加参数
    $('#add-parameter').on('click', function() {
        layer.open({
            type: 1,
            title: '添加参数',
            area: ['600px', '400px'],
            content: $('#add-parameter-form').html(),
            btn: ['确定', '取消'],
            yes: function(index) {
                // 获取表单数据并添加参数
                var formData = $('#temp-param-form').serializeArray();
                var newParam = {
                    id: 'param_' + Date.now(),
                    elementUuid: 'uuid_' + Date.now(),
                    parameterName: '',
                    parameterLabel: '',
                    parameterType: 'text',
                    isRequired: false,
                    isEnabled: true,
                    defaultValue: '',
                    validationRules: {},
                    displayOrder: parameters.length + 1,
                    parameterDescription: ''
                };
                
                // 更新参数数据
                formData.forEach(function(item) {
                    if (item.name === 'maxLength') {
                        newParam.validationRules.maxLength = item.value;
                    } else {
                        newParam[item.name] = item.value;
                    }
                });
                
                parameters.push(newParam);
                renderParameters();
                layer.close(index);
            }
        });
    });
    
    // 表单提交
    form.on('submit(submit)', function(data) {
        // 收集参数数据
        var formParameters = [];
        $('.parameter-item').each(function() {
            var $item = $(this);
            var paramId = $item.data('id');
            var param = {};
            
            $item.find('input, select, textarea').each(function() {
                var $input = $(this);
                var name = $input.attr('name');
                if (name && name.indexOf('parameters[' + paramId + ']') === 0) {
                    var fieldName = name.replace('parameters[' + paramId + '][', '').replace(']', '');
                    var value = $input.val();
                    
                    if ($input.attr('type') === 'checkbox') {
                        value = $input.is(':checked');
                    } else if ($input.attr('type') === 'number') {
                        value = parseInt(value) || 0;
                    }
                    
                    if (fieldName === 'maxLength') {
                        if (!param.validationRules) param.validationRules = {};
                        param.validationRules.maxLength = value;
                    } else {
                        param[fieldName] = value;
                    }
                }
            });
            
            if (param.id) {
                formParameters.push(param);
            }
        });
        
        // 更新表单数据
        data.field.parameters = formParameters;
        
        var loadingIndex = layer.load(2, {content: '正在保存配置...'});
        
        $.post('{:url("poster_template/config_edit")}', data.field, function(res) {
            layer.close(loadingIndex);
            
            if (res.code == 1) {
                layer.msg('配置保存成功', {
                    icon: 1,
                    time: 2000
                }, function() {
                    location.reload();
                });
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }).fail(function() {
            layer.close(loadingIndex);
            layer.msg('网络错误，请重试', {icon: 2});
        });
        
        return false;
    });
    
    // 初始化
    renderParameters();
});
</script>

<style>
.stats-info p {
    margin: 5px 0;
}

.parameter-item {
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    margin-bottom: 10px;
}

.parameter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #f8f8f8;
    border-bottom: 1px solid #e6e6e6;
}

.parameter-title {
    display: flex;
    align-items: center;
    gap: 8px;
}

.parameter-name {
    font-weight: bold;
}

.parameter-content {
    padding: 15px;
}

.parameter-actions {
    display: flex;
    gap: 5px;
}
</style>
</div>
