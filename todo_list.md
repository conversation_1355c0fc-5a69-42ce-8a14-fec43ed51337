# 移动端添加虚拟商品评价功能

## 最终目标
在移动端(API)实现类似后台的虚拟评论功能，允许特定会员等级的用户可以手动添加商品评论，包括设置评论的会员昵称、头像、等级、评价时间、评价等级、评价图片和评价内容。

## 子任务列表

### 代码实现
- [x] 在API控制器中新增虚拟评价接口方法 `addVirtualComment`
- [x] 在API逻辑层中新增对应的业务逻辑方法 `addVirtualComment`
- [x] 创建API验证器 `VirtualComment.php` 用于验证提交的数据
- [x] 增加会员等级权限验证逻辑
- [x] 实现虚拟评价的添加功能，包括评价内容和图片上传

### 数据库调整
- [x] 检查 `ls_goods_comment` 表结构，确认是否需要添加 `created_by_user_id` 字段

### 权限控制
- [x] 实现基于会员等级的权限控制
- [x] 添加每日添加虚拟评价数量的限制功能

### 测试与验证
- [ ] 测试接口功能是否正常
- [ ] 验证权限控制是否有效
- [ ] 确认评价和图片是否正确保存
- [ ] 检查前端展示是否正常

### 文档
- [x] 编写API接口文档
- [x] 记录实现过程中的注意事项 