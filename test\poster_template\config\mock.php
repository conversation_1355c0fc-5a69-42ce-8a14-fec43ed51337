<?php
/**
 * 动态参数模板系统Mock数据配置
 */

return [
    // 模板Mock数据
    'templates' => [
        [
            'id' => '1',
            'title' => '测试模板1 - 日签海报',
            'description' => '这是一个测试用的日签海报模板',
            'thumbnail' => 'http://localhost:7001/static/1-cover.jpg',
            'category' => 'poster',
            'tags' => ['日签', '海报'],
            'width' => 1242,
            'height' => 2208,
            'textElementsCount' => 3,
            'createdAt' => '2025-01-16T10:00:00Z',
            'updatedAt' => '2025-01-16T10:00:00Z',
        ],
        [
            'id' => '2',
            'title' => '测试模板2 - 商品宣传',
            'description' => '这是一个测试用的商品宣传模板',
            'thumbnail' => 'http://localhost:7001/static/2-cover.jpg',
            'category' => 'product',
            'tags' => ['商品', '宣传'],
            'width' => 800,
            'height' => 1200,
            'textElementsCount' => 5,
            'createdAt' => '2025-01-16T10:00:00Z',
            'updatedAt' => '2025-01-16T10:00:00Z',
        ],
    ],
    
    // 模板解析Mock数据
    'template_parse' => [
        'templateId' => '1',
        'templateTitle' => '测试模板1 - 日签海报',
        'textElements' => [
            [
                'uuid' => 'test-uuid-001',
                'type' => 'w-text',
                'text' => '你好，世界',
                'position' => [
                    'left' => 100,
                    'top' => 200,
                    'width' => 300,
                    'height' => 50,
                ],
                'style' => [
                    'fontSize' => 24,
                    'color' => '#000000',
                    'textAlign' => 'center',
                ],
            ],
        ],
        'parameterCandidates' => [
            [
                'elementUuid' => 'test-uuid-001',
                'suggestedName' => 'greeting',
                'suggestedLabel' => '问候语',
                'suggestedDescription' => '可自定义的问候语文本',
                'suggestedType' => 'text',
                'originalText' => '你好，世界',
                'textCategory' => 'general',
                'maxLength' => 50,
                'isRequired' => true,
            ],
        ],
    ],
    
    // 参数数据Mock
    'parameter_data' => [
        'id' => 'test-data-001',
        'configId' => 'test-config-001',
        'templateId' => '1',
        'parameterValues' => [
            'greeting' => '你好，测试',
            'content' => '这是测试内容',
        ],
    ],
    
    // API响应Mock
    'api_responses' => [
        'success' => [
            'code' => 200,
            'message' => 'success',
            'data' => [],
        ],
        'error' => [
            'code' => 400,
            'message' => '请求参数错误',
            'error' => [
                'reason' => '详细错误原因',
                'timestamp' => '2025-01-16T10:00:00Z',
            ],
        ],
    ],
];
