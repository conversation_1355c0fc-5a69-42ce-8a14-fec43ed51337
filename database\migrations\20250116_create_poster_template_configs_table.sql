-- 动态参数模板系统 - 模板参数配置表
-- 创建时间: 2025-01-16
-- 描述: 存储模板参数配置信息

CREATE TABLE IF NOT EXISTS `ls_poster_template_configs` (
  `id` VARCHAR(32) NOT NULL COMMENT '配置ID',
  `template_id` VARCHAR(32) NOT NULL COMMENT '迅排设计模板ID',
  `template_title` VARCHAR(255) NULL COMMENT '模板标题',
  `config_name` VARCHAR(255) NOT NULL COMMENT '配置名称',
  `config_description` TEXT NULL COMMENT '配置描述',
  `parameters` JSON NOT NULL COMMENT '参数定义JSON',
  `created_by` VARCHAR(32) NULL COMMENT '创建者ID',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` TINYINT(1) DEFAULT 1 COMMENT '状态：1启用 0禁用',
  
  PRIMARY KEY (`id`),
  INDEX `idx_template_id` (`template_id`),
  INDEX `idx_created_by` (`created_by`),
  INDEX `idx_status` (`status`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模板参数配置表';

-- 添加JSON字段的虚拟列索引（用于参数搜索）
-- ALTER TABLE `ls_poster_template_configs` 
-- ADD COLUMN `parameter_count` INT GENERATED ALWAYS AS (JSON_LENGTH(parameters)) VIRTUAL,
-- ADD INDEX `idx_parameter_count` (`parameter_count`);
