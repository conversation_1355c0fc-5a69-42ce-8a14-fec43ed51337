<?php
/**
 * 动态参数模板系统一键安装脚本
 * 使用方法：在项目根目录执行 php install_poster_template_system.php
 */

// 检查运行环境
if (php_sapi_name() !== 'cli') {
    die("This script must be run from the command line.\n");
}

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入ThinkPHP框架
require_once __DIR__ . '/vendor/autoload.php';

class PosterTemplateInstaller
{
    private $db;
    private $config;
    
    public function __construct()
    {
        // 初始化ThinkPHP应用
        $this->initApp();
        
        // 获取数据库连接
        $this->db = \think\facade\Db::connect();
        
        // 加载配置
        $this->config = \think\facade\Config::get('database');
    }
    
    /**
     * 初始化应用
     */
    private function initApp()
    {
        $app = new \think\App();
        $app->initialize();
        \think\facade\App::setInstance($app);
    }
    
    /**
     * 执行安装
     */
    public function install()
    {
        $this->printHeader();
        
        try {
            // 1. 检查系统要求
            $this->checkRequirements();
            
            // 2. 检查数据库连接
            $this->checkDatabase();
            
            // 3. 创建数据库表
            $this->createTables();
            
            // 4. 插入菜单数据
            $this->insertMenuData();
            
            // 5. 生成种子数据
            $this->generateSeedData();
            
            // 6. 设置权限
            $this->setPermissions();
            
            // 7. 完成安装
            $this->completeInstallation();
            
        } catch (Exception $e) {
            $this->printError("安装失败: " . $e->getMessage());
            exit(1);
        }
    }
    
    /**
     * 打印头部信息
     */
    private function printHeader()
    {
        echo "\n";
        echo "========================================\n";
        echo "  动态参数模板系统安装程序\n";
        echo "  版本: 1.0.0\n";
        echo "  日期: " . date('Y-m-d H:i:s') . "\n";
        echo "========================================\n\n";
    }
    
    /**
     * 检查系统要求
     */
    private function checkRequirements()
    {
        $this->printStep("检查系统要求");
        
        // 检查PHP版本
        if (version_compare(PHP_VERSION, '7.0.0', '<')) {
            throw new Exception("PHP版本必须 >= 7.0.0，当前版本: " . PHP_VERSION);
        }
        $this->printSuccess("PHP版本: " . PHP_VERSION);
        
        // 检查必需的扩展
        $requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'curl', 'openssl', 'mbstring'];
        foreach ($requiredExtensions as $ext) {
            if (!extension_loaded($ext)) {
                throw new Exception("缺少必需的PHP扩展: {$ext}");
            }
        }
        $this->printSuccess("PHP扩展检查通过");
        
        // 检查目录权限
        $writableDirs = ['runtime', 'public/uploads'];
        foreach ($writableDirs as $dir) {
            if (!is_writable($dir)) {
                throw new Exception("目录不可写: {$dir}");
            }
        }
        $this->printSuccess("目录权限检查通过");
    }
    
    /**
     * 检查数据库连接
     */
    private function checkDatabase()
    {
        $this->printStep("检查数据库连接");
        
        try {
            $result = $this->db->query("SELECT VERSION() as version");
            $version = $result[0]['version'];
            $this->printSuccess("数据库连接成功，MySQL版本: {$version}");
            
            // 检查MySQL版本是否支持JSON字段
            if (version_compare($version, '5.7.0', '<')) {
                $this->printWarning("MySQL版本 < 5.7.0，JSON字段功能可能受限");
            }
            
        } catch (Exception $e) {
            throw new Exception("数据库连接失败: " . $e->getMessage());
        }
    }
    
    /**
     * 创建数据库表
     */
    private function createTables()
    {
        $this->printStep("创建数据库表");
        
        $sqlFile = __DIR__ . '/database/migrations/install_poster_template_system.sql';
        if (!file_exists($sqlFile)) {
            throw new Exception("SQL文件不存在: {$sqlFile}");
        }
        
        $sql = file_get_contents($sqlFile);
        $statements = explode(';', $sql);
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (empty($statement) || strpos($statement, '--') === 0) {
                continue;
            }
            
            try {
                $this->db->execute($statement);
            } catch (Exception $e) {
                // 忽略表已存在的错误
                if (strpos($e->getMessage(), 'already exists') === false) {
                    throw $e;
                }
            }
        }
        
        $this->printSuccess("数据库表创建完成");
    }
    
    /**
     * 插入菜单数据
     */
    private function insertMenuData()
    {
        $this->printStep("插入菜单数据");
        
        $sqlFile = __DIR__ . '/database/migrations/add_poster_template_menu.sql';
        if (!file_exists($sqlFile)) {
            throw new Exception("菜单SQL文件不存在: {$sqlFile}");
        }
        
        $sql = file_get_contents($sqlFile);
        $statements = explode(';', $sql);
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (empty($statement) || strpos($statement, '--') === 0) {
                continue;
            }
            
            try {
                $this->db->execute($statement);
            } catch (Exception $e) {
                // 忽略重复插入的错误
                if (strpos($e->getMessage(), 'Duplicate entry') === false) {
                    throw $e;
                }
            }
        }
        
        $this->printSuccess("菜单数据插入完成");
    }
    
    /**
     * 生成种子数据
     */
    private function generateSeedData()
    {
        $this->printStep("生成种子数据");
        
        try {
            // 使用PHP种子数据生成器
            require_once __DIR__ . '/test/poster_template/tools/seeders/PosterTemplateSeeder.php';
            \test\poster_template\tools\seeders\PosterTemplateSeeder::run();
            
            $this->printSuccess("种子数据生成完成");
        } catch (Exception $e) {
            $this->printWarning("种子数据生成失败: " . $e->getMessage());
        }
    }
    
    /**
     * 设置权限
     */
    private function setPermissions()
    {
        $this->printStep("设置文件权限");
        
        $dirs = [
            'runtime' => 0755,
            'public/uploads' => 0755,
            'public/uploads/poster_template' => 0755,
        ];
        
        foreach ($dirs as $dir => $permission) {
            if (!is_dir($dir)) {
                mkdir($dir, $permission, true);
            }
            chmod($dir, $permission);
        }
        
        $this->printSuccess("文件权限设置完成");
    }
    
    /**
     * 完成安装
     */
    private function completeInstallation()
    {
        $this->printStep("完成安装");
        
        // 创建安装标记文件
        $installFile = __DIR__ . '/runtime/poster_template_installed.lock';
        file_put_contents($installFile, json_encode([
            'installed_at' => date('Y-m-d H:i:s'),
            'version' => '1.0.0',
            'installer' => get_current_user(),
        ]));
        
        echo "\n";
        echo "========================================\n";
        echo "  安装完成！\n";
        echo "========================================\n";
        echo "系统已成功安装，您现在可以：\n\n";
        echo "1. 访问后台管理: /admin/poster_template/config_list\n";
        echo "2. 访问用户端: /poster\n";
        echo "3. 查看API文档: /api/poster-external/health\n\n";
        echo "配置建议：\n";
        echo "1. 复制 .env.poster_template.example 为 .env.poster_template\n";
        echo "2. 修改其中的API配置信息\n";
        echo "3. 配置迅排设计API的回调地址\n\n";
        echo "如需卸载，请运行: php uninstall_poster_template_system.php\n";
        echo "========================================\n\n";
    }
    
    /**
     * 打印步骤信息
     */
    private function printStep($message)
    {
        echo "[STEP] {$message}...\n";
    }
    
    /**
     * 打印成功信息
     */
    private function printSuccess($message)
    {
        echo "[OK] {$message}\n";
    }
    
    /**
     * 打印警告信息
     */
    private function printWarning($message)
    {
        echo "[WARNING] {$message}\n";
    }
    
    /**
     * 打印错误信息
     */
    private function printError($message)
    {
        echo "[ERROR] {$message}\n";
    }
}

// 执行安装
try {
    $installer = new PosterTemplateInstaller();
    $installer->install();
} catch (Exception $e) {
    echo "[FATAL ERROR] " . $e->getMessage() . "\n";
    exit(1);
}
