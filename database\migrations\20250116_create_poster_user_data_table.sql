-- 动态参数模板系统 - 用户参数数据表
-- 创建时间: 2025-01-16
-- 描述: 存储用户填写的参数数据

CREATE TABLE IF NOT EXISTS `ls_poster_user_data` (
  `id` VARCHAR(32) NOT NULL COMMENT '数据ID',
  `config_id` VARCHAR(32) NOT NULL COMMENT '配置ID',
  `user_id` VARCHAR(32) NULL COMMENT '用户ID',
  `session_id` VARCHAR(64) NULL COMMENT '会话ID（匿名用户）',
  `parameter_values` JSON NOT NULL COMMENT '用户填写的参数值',
  `is_draft` BOOLEAN DEFAULT TRUE COMMENT '是否为草稿',
  `preview_url` VARCHAR(500) NULL COMMENT '预览页面URL',
  `generated_image_url` VARCHAR(500) NULL COMMENT '生成的图片URL',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  INDEX `idx_config_id` (`config_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_session_id` (`session_id`),
  INDEX `idx_is_draft` (`is_draft`),
  INDEX `idx_created_at` (`created_at`),
  INDEX `idx_user_draft` (`user_id`, `is_draft`),
  INDEX `idx_session_draft` (`session_id`, `is_draft`),
  
  CONSTRAINT `fk_poster_user_data_config` 
    FOREIGN KEY (`config_id`) 
    REFERENCES `ls_poster_template_configs`(`id`) 
    ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户参数数据表';

-- 添加检查约束确保用户ID或会话ID至少有一个不为空
-- ALTER TABLE `ls_poster_user_data` 
-- ADD CONSTRAINT `chk_user_or_session` 
-- CHECK ((`user_id` IS NOT NULL) OR (`session_id` IS NOT NULL));
