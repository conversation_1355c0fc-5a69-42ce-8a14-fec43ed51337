-- 动态参数模板系统数据库安装脚本
-- 版本: 1.0.0
-- 创建时间: 2025-01-16
-- 描述: 一键安装动态参数模板系统所需的所有数据库表

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ============================================================================
-- 1. 模板参数配置表
-- ============================================================================
CREATE TABLE IF NOT EXISTS `ls_poster_template_configs` (
  `id` VARCHAR(32) NOT NULL COMMENT '配置ID',
  `template_id` VARCHAR(32) NOT NULL COMMENT '迅排设计模板ID',
  `template_title` VARCHAR(255) NULL COMMENT '模板标题',
  `config_name` VARCHAR(255) NOT NULL COMMENT '配置名称',
  `config_description` TEXT NULL COMMENT '配置描述',
  `parameters` JSON NOT NULL COMMENT '参数定义JSON',
  `created_by` VARCHAR(32) NULL COMMENT '创建者ID',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` TINYINT(1) DEFAULT 1 COMMENT '状态：1启用 0禁用',
  
  PRIMARY KEY (`id`),
  INDEX `idx_template_id` (`template_id`),
  INDEX `idx_created_by` (`created_by`),
  INDEX `idx_status` (`status`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模板参数配置表';

-- ============================================================================
-- 2. 用户参数数据表
-- ============================================================================
CREATE TABLE IF NOT EXISTS `ls_poster_user_data` (
  `id` VARCHAR(32) NOT NULL COMMENT '数据ID',
  `config_id` VARCHAR(32) NOT NULL COMMENT '配置ID',
  `user_id` VARCHAR(32) NULL COMMENT '用户ID',
  `session_id` VARCHAR(64) NULL COMMENT '会话ID（匿名用户）',
  `parameter_values` JSON NOT NULL COMMENT '用户填写的参数值',
  `is_draft` BOOLEAN DEFAULT TRUE COMMENT '是否为草稿',
  `preview_url` VARCHAR(500) NULL COMMENT '预览页面URL',
  `generated_image_url` VARCHAR(500) NULL COMMENT '生成的图片URL',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  INDEX `idx_config_id` (`config_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_session_id` (`session_id`),
  INDEX `idx_is_draft` (`is_draft`),
  INDEX `idx_created_at` (`created_at`),
  INDEX `idx_user_draft` (`user_id`, `is_draft`),
  INDEX `idx_session_draft` (`session_id`, `is_draft`),
  
  CONSTRAINT `fk_poster_user_data_config` 
    FOREIGN KEY (`config_id`) 
    REFERENCES `ls_poster_template_configs`(`id`) 
    ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户参数数据表';

-- ============================================================================
-- 3. 图片生成记录表
-- ============================================================================
CREATE TABLE IF NOT EXISTS `ls_poster_generation_records` (
  `id` VARCHAR(32) NOT NULL COMMENT '记录ID',
  `data_id` VARCHAR(32) NOT NULL COMMENT '参数数据ID',
  `image_url` VARCHAR(500) NOT NULL COMMENT '生成的图片URL',
  `generation_options` JSON NULL COMMENT '生成选项（宽度、高度、质量等）',
  `generation_time` DECIMAL(10,3) NULL COMMENT '生成耗时（秒）',
  `file_size` INT NULL COMMENT '文件大小（字节）',
  `image_width` INT NULL COMMENT '图片宽度',
  `image_height` INT NULL COMMENT '图片高度',
  `image_format` VARCHAR(10) NULL COMMENT '图片格式（jpg、png等）',
  `quality` DECIMAL(3,2) NULL COMMENT '图片质量（0.1-1.0）',
  `status` TINYINT(1) DEFAULT 1 COMMENT '状态：1成功 0失败',
  `error_message` TEXT NULL COMMENT '错误信息（如果生成失败）',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  PRIMARY KEY (`id`),
  INDEX `idx_data_id` (`data_id`),
  INDEX `idx_created_at` (`created_at`),
  INDEX `idx_status` (`status`),
  INDEX `idx_generation_time` (`generation_time`),
  INDEX `idx_file_size` (`file_size`),
  INDEX `idx_data_status` (`data_id`, `status`),
  
  CONSTRAINT `fk_poster_generation_records_data` 
    FOREIGN KEY (`data_id`) 
    REFERENCES `ls_poster_user_data`(`id`) 
    ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片生成记录表';

-- ============================================================================
-- 4. 插入初始数据（可选）
-- ============================================================================

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 显示安装结果
SELECT 'Poster Template System Database Installation Completed!' as Status;
