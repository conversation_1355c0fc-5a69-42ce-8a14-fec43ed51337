{layout name="layout1" /}
<div class="layui-fluid">
<div class="layui-card">
    <div class="layui-card-header">
        <span>添加模板配置</span>
        <div class="layui-btn-group fr">
            <a class="layui-btn layui-btn-primary layui-btn-sm" href="{:url('poster_template/config_list')}">
                <i class="layui-icon layui-icon-return"></i>返回列表
            </a>
        </div>
    </div>
    <div class="layui-card-body">
        <form class="layui-form" lay-filter="config-form">
            <div class="layui-row layui-col-space20">
                <!-- 左侧：模板选择 -->
                <div class="layui-col-md6">
                    <div class="layui-card">
                        <div class="layui-card-header">选择模板</div>
                        <div class="layui-card-body">
                            <div class="layui-form-item">
                                <label class="layui-form-label">搜索模板</label>
                                <div class="layui-input-block">
                                    <input type="text" id="template-search" placeholder="输入关键词搜索模板" class="layui-input">
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">模板分类</label>
                                <div class="layui-input-block">
                                    <select id="template-category">
                                        <option value="">全部分类</option>
                                        <option value="poster">海报</option>
                                        <option value="product">商品</option>
                                        <option value="social">社交</option>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- 模板列表 -->
                            <div id="template-list" class="template-list">
                                <div class="loading-placeholder">
                                    <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>
                                    正在加载模板...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧：配置信息 -->
                <div class="layui-col-md6">
                    <div class="layui-card">
                        <div class="layui-card-header">配置信息</div>
                        <div class="layui-card-body">
                            <div class="layui-form-item">
                                <label class="layui-form-label">模板ID</label>
                                <div class="layui-input-block">
                                    <input type="text" name="template_id" readonly placeholder="请先选择模板" class="layui-input">
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">配置名称</label>
                                <div class="layui-input-block">
                                    <input type="text" name="config_name" required lay-verify="required" placeholder="请输入配置名称" class="layui-input">
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">配置描述</label>
                                <div class="layui-input-block">
                                    <textarea name="config_description" placeholder="请输入配置描述" class="layui-textarea"></textarea>
                                </div>
                            </div>
                            
                            <!-- 模板预览 -->
                            <div id="template-preview" class="template-preview" style="display: none;">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">模板预览</label>
                                    <div class="layui-input-block">
                                        <div class="preview-container">
                                            <img id="preview-image" src="" alt="模板预览">
                                            <div class="preview-info">
                                                <p><strong>标题：</strong><span id="preview-title"></span></p>
                                                <p><strong>尺寸：</strong><span id="preview-size"></span></p>
                                                <p><strong>文本元素：</strong><span id="preview-elements"></span></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button class="layui-btn" lay-submit lay-filter="submit" disabled>
                                        <i class="layui-icon layui-icon-add-1"></i>创建配置
                                    </button>
                                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 模板项模板 -->
<script type="text/html" id="template-item-tpl">
    {{# layui.each(d.data, function(index, item){ }}
    <div class="template-item" data-id="{{item.id}}" data-title="{{item.title}}" data-thumbnail="{{item.thumbnail}}" data-width="{{item.width}}" data-height="{{item.height}}" data-elements="{{item.textElementsCount}}">
        <div class="template-thumbnail">
            <img src="{{item.thumbnail}}" alt="{{item.title}}">
            <div class="template-overlay">
                <button class="layui-btn layui-btn-sm select-btn">选择</button>
            </div>
        </div>
        <div class="template-info">
            <h4>{{item.title}}</h4>
            <p>{{item.width}} x {{item.height}}</p>
            <p>{{item.textElementsCount}} 个文本元素</p>
        </div>
    </div>
    {{# }); }}
</script>

{/block}

{block name="script"}
<script>
layui.use(['form', 'layer', 'laytpl'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var laytpl = layui.laytpl;
    
    var selectedTemplateId = '';
    var loadingIndex = null;
    
    // 加载模板列表
    function loadTemplates(params = {}) {
        var defaultParams = {
            page: 1,
            pageSize: 12
        };
        params = Object.assign(defaultParams, params);
        
        $.get('{:url("poster_template/get_templates")}', params, function(res) {
            if (res.code == 1) {
                var getTpl = document.getElementById('template-item-tpl').innerHTML;
                var view = document.getElementById('template-list');
                laytpl(getTpl).render(res, function(html) {
                    view.innerHTML = html;
                });
                
                // 绑定选择事件
                bindTemplateSelect();
            } else {
                document.getElementById('template-list').innerHTML = 
                    '<div class="error-placeholder">加载模板失败：' + res.msg + '</div>';
            }
        }).fail(function() {
            document.getElementById('template-list').innerHTML = 
                '<div class="error-placeholder">网络错误，请重试</div>';
        });
    }
    
    // 绑定模板选择事件
    function bindTemplateSelect() {
        $('.template-item').on('click', function() {
            var $this = $(this);
            var templateId = $this.data('id');
            var title = $this.data('title');
            var thumbnail = $this.data('thumbnail');
            var width = $this.data('width');
            var height = $this.data('height');
            var elements = $this.data('elements');
            
            // 更新选中状态
            $('.template-item').removeClass('selected');
            $this.addClass('selected');
            
            // 更新表单
            $('input[name="template_id"]').val(templateId);
            $('input[name="config_name"]').val(title + '_配置');
            
            // 更新预览
            $('#preview-image').attr('src', thumbnail);
            $('#preview-title').text(title);
            $('#preview-size').text(width + ' x ' + height);
            $('#preview-elements').text(elements + ' 个');
            $('#template-preview').show();
            
            // 启用提交按钮
            $('button[lay-filter="submit"]').removeAttr('disabled');
            
            selectedTemplateId = templateId;
        });
    }
    
    // 搜索模板
    $('#template-search').on('input', function() {
        var keyword = $(this).val();
        var category = $('#template-category').val();
        
        loadTemplates({
            keyword: keyword,
            category: category
        });
    });
    
    // 分类筛选
    $('#template-category').on('change', function() {
        var category = $(this).val();
        var keyword = $('#template-search').val();
        
        loadTemplates({
            keyword: keyword,
            category: category
        });
    });
    
    // 表单提交
    form.on('submit(submit)', function(data) {
        if (!selectedTemplateId) {
            layer.msg('请先选择模板');
            return false;
        }
        
        loadingIndex = layer.load(2, {content: '正在创建配置...'});
        
        $.post('{:url("poster_template/config_add")}', data.field, function(res) {
            layer.close(loadingIndex);
            
            if (res.code == 1) {
                layer.msg('配置创建成功', {
                    icon: 1,
                    time: 2000
                }, function() {
                    location.href = '{:url("poster_template/config_list")}';
                });
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }).fail(function() {
            layer.close(loadingIndex);
            layer.msg('网络错误，请重试', {icon: 2});
        });
        
        return false;
    });
    
    // 初始加载模板
    loadTemplates();
});
</script>

<style>
.template-list {
    max-height: 500px;
    overflow-y: auto;
}

.template-item {
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    margin-bottom: 10px;
    padding: 10px;
    cursor: pointer;
    transition: all 0.3s;
}

.template-item:hover {
    border-color: #1E9FFF;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.template-item.selected {
    border-color: #1E9FFF;
    background-color: #f0f9ff;
}

.template-thumbnail {
    position: relative;
    text-align: center;
    margin-bottom: 10px;
}

.template-thumbnail img {
    max-width: 100%;
    max-height: 150px;
    border-radius: 4px;
}

.template-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s;
}

.template-item:hover .template-overlay {
    opacity: 1;
}

.template-info h4 {
    margin: 0 0 5px 0;
    font-size: 14px;
    font-weight: bold;
}

.template-info p {
    margin: 0;
    font-size: 12px;
    color: #666;
}

.preview-container {
    text-align: center;
}

.preview-container img {
    max-width: 200px;
    max-height: 300px;
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    margin-bottom: 10px;
}

.preview-info {
    text-align: left;
    font-size: 12px;
}

.preview-info p {
    margin: 5px 0;
}

.loading-placeholder,
.error-placeholder {
    text-align: center;
    padding: 50px 20px;
    color: #999;
}

.error-placeholder {
    color: #ff5722;
}
</style>
</div>
