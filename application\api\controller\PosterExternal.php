<?php
/**
 * 动态参数模板系统外部API控制器
 * 供迅排设计服务调用
 */

namespace app\api\controller;

use app\common\model\PosterTemplateConfig;
use app\common\model\PosterUserData;
use think\Controller;
use think\facade\Log;
use think\facade\Validate;

class PosterExternal extends Controller
{
    /**
     * 获取参数数据
     * GET /api/external/parameter-data/{dataId}
     */
    public function getParameterData($dataId = '')
    {
        try {
            if (empty($dataId)) {
                return $this->errorResponse('Parameter data ID is required', 400);
            }
            
            // 查询用户数据
            $userData = PosterUserData::where('id', $dataId)
                ->with(['config'])
                ->find();
            
            if (!$userData) {
                Log::warning('Parameter data not found', ['data_id' => $dataId]);
                return $this->errorResponse('Parameter data not found', 404);
            }
            
            // 构建响应数据
            $responseData = [
                'id' => $userData->id,
                'configId' => $userData->config_id,
                'templateId' => $userData->config->template_id ?? '',
                'parameterValues' => $userData->parameter_values
            ];
            
            Log::info('Parameter data retrieved', [
                'data_id' => $dataId,
                'config_id' => $userData->config_id
            ]);
            
            return $this->successResponse($responseData);
            
        } catch (\Exception $e) {
            Log::error('Get parameter data error', [
                'data_id' => $dataId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse('Internal server error', 500);
        }
    }
    
    /**
     * 获取参数配置
     * GET /api/external/parameter-config/{configId}
     */
    public function getParameterConfig($configId = '')
    {
        try {
            if (empty($configId)) {
                return $this->errorResponse('Parameter config ID is required', 400);
            }
            
            // 查询配置数据
            $config = PosterTemplateConfig::where('id', $configId)
                ->where('status', PosterTemplateConfig::STATUS_ENABLED)
                ->find();
            
            if (!$config) {
                Log::warning('Parameter config not found', ['config_id' => $configId]);
                return $this->errorResponse('Parameter config not found', 404);
            }
            
            // 构建响应数据
            $responseData = [
                'id' => $config->id,
                'templateId' => $config->template_id,
                'parameters' => $config->parameters
            ];
            
            Log::info('Parameter config retrieved', [
                'config_id' => $configId,
                'template_id' => $config->template_id
            ]);
            
            return $this->successResponse($responseData);
            
        } catch (\Exception $e) {
            Log::error('Get parameter config error', [
                'config_id' => $configId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse('Internal server error', 500);
        }
    }
    
    /**
     * 健康检查
     * GET /api/external/health
     */
    public function health()
    {
        try {
            // 检查数据库连接
            $dbStatus = 'ok';
            try {
                PosterTemplateConfig::count();
            } catch (\Exception $e) {
                $dbStatus = 'error';
                Log::error('Database health check failed', ['error' => $e->getMessage()]);
            }
            
            $responseData = [
                'status' => $dbStatus === 'ok' ? 'ok' : 'error',
                'timestamp' => date('c'),
                'services' => [
                    'database' => $dbStatus
                ]
            ];
            
            $httpCode = $dbStatus === 'ok' ? 200 : 503;
            
            return $this->successResponse($responseData, $httpCode);
            
        } catch (\Exception $e) {
            Log::error('Health check error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse('Health check failed', 500);
        }
    }
    
    /**
     * 批量获取参数数据
     * POST /api/external/parameter-data/batch
     */
    public function batchGetParameterData()
    {
        try {
            $input = $this->request->post();
            
            // 验证输入
            $validate = Validate::make([
                'dataIds' => 'require|array',
                'dataIds.*' => 'require|alphaNum'
            ]);
            
            if (!$validate->check($input)) {
                return $this->errorResponse($validate->getError(), 400);
            }
            
            $dataIds = $input['dataIds'];
            if (count($dataIds) > 50) {
                return $this->errorResponse('Too many data IDs, maximum 50 allowed', 400);
            }
            
            // 批量查询
            $userDataList = PosterUserData::whereIn('id', $dataIds)
                ->with(['config'])
                ->select();
            
            $responseData = [];
            foreach ($userDataList as $userData) {
                $responseData[] = [
                    'id' => $userData->id,
                    'configId' => $userData->config_id,
                    'templateId' => $userData->config->template_id ?? '',
                    'parameterValues' => $userData->parameter_values
                ];
            }
            
            Log::info('Batch parameter data retrieved', [
                'requested_count' => count($dataIds),
                'found_count' => count($responseData)
            ]);
            
            return $this->successResponse([
                'items' => $responseData,
                'total' => count($responseData),
                'requested' => count($dataIds)
            ]);
            
        } catch (\Exception $e) {
            Log::error('Batch get parameter data error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse('Internal server error', 500);
        }
    }
    
    /**
     * 更新用户数据的预览URL
     * PUT /api/external/parameter-data/{dataId}/preview-url
     */
    public function updatePreviewUrl($dataId = '')
    {
        try {
            if (empty($dataId)) {
                return $this->errorResponse('Parameter data ID is required', 400);
            }
            
            $input = $this->request->put();
            
            // 验证输入
            $validate = Validate::make([
                'previewUrl' => 'require|url'
            ]);
            
            if (!$validate->check($input)) {
                return $this->errorResponse($validate->getError(), 400);
            }
            
            // 更新预览URL
            $result = PosterUserData::where('id', $dataId)->update([
                'preview_url' => $input['previewUrl']
            ]);
            
            if ($result === false) {
                return $this->errorResponse('Parameter data not found', 404);
            }
            
            Log::info('Preview URL updated', [
                'data_id' => $dataId,
                'preview_url' => $input['previewUrl']
            ]);
            
            return $this->successResponse([
                'id' => $dataId,
                'previewUrl' => $input['previewUrl'],
                'updatedAt' => date('c')
            ]);
            
        } catch (\Exception $e) {
            Log::error('Update preview URL error', [
                'data_id' => $dataId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse('Internal server error', 500);
        }
    }
    
    /**
     * 成功响应
     * @param mixed $data
     * @param int $code
     * @return \think\Response
     */
    protected function successResponse($data = [], $code = 200)
    {
        return json([
            'code' => 200,
            'message' => 'success',
            'data' => $data
        ], $code);
    }
    
    /**
     * 错误响应
     * @param string $message
     * @param int $code
     * @return \think\Response
     */
    protected function errorResponse($message, $code = 400)
    {
        return json([
            'code' => $code,
            'message' => $message,
            'error' => [
                'reason' => $message,
                'timestamp' => date('c')
            ]
        ], $code);
    }
}
