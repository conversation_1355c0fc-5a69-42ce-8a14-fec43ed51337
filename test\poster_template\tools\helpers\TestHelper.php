<?php
/**
 * 动态参数模板系统测试辅助类
 */

namespace test\poster_template\tools\helpers;

class TestHelper
{
    /**
     * 生成测试用的UUID
     * @return string
     */
    public static function generateTestId()
    {
        return 'test-' . uniqid() . '-' . mt_rand(1000, 9999);
    }
    
    /**
     * 生成测试用的配置数据
     * @param array $overrides
     * @return array
     */
    public static function generateTestConfig($overrides = [])
    {
        $default = [
            'id' => self::generateTestId(),
            'template_id' => '1',
            'template_title' => '测试模板',
            'config_name' => '测试配置',
            'config_description' => '这是一个测试配置',
            'parameters' => json_encode([
                [
                    'id' => 'param-' . uniqid(),
                    'elementUuid' => 'uuid-' . uniqid(),
                    'parameterName' => 'test_param',
                    'parameterLabel' => '测试参数',
                    'parameterType' => 'text',
                    'isRequired' => true,
                    'defaultValue' => '测试值',
                    'validationRules' => ['maxLength' => 100],
                    'displayOrder' => 1,
                    'isEnabled' => true,
                ]
            ]),
            'created_by' => 'test-admin',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
            'status' => 1,
        ];
        
        return array_merge($default, $overrides);
    }
    
    /**
     * 生成测试用的用户数据
     * @param array $overrides
     * @return array
     */
    public static function generateTestUserData($overrides = [])
    {
        $default = [
            'id' => self::generateTestId(),
            'config_id' => 'test-config-001',
            'user_id' => 'test-user-001',
            'session_id' => null,
            'parameter_values' => json_encode([
                'test_param' => '测试参数值',
            ]),
            'is_draft' => 0,
            'preview_url' => null,
            'generated_image_url' => null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ];
        
        return array_merge($default, $overrides);
    }
    
    /**
     * 清理测试数据
     * @param array $tables
     */
    public static function cleanTestData($tables = [])
    {
        $defaultTables = [
            'poster_template_configs',
            'poster_user_data',
            'poster_generation_records',
        ];
        
        $tables = empty($tables) ? $defaultTables : $tables;
        
        foreach ($tables as $table) {
            \think\facade\Db::table($table)->where('id', 'like', 'test-%')->delete();
        }
    }
    
    /**
     * 创建Mock API响应
     * @param array $data
     * @param int $code
     * @param string $message
     * @return array
     */
    public static function createMockApiResponse($data = [], $code = 200, $message = 'success')
    {
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];
    }
    
    /**
     * 验证API响应格式
     * @param array $response
     * @return bool
     */
    public static function validateApiResponse($response)
    {
        return isset($response['code']) && 
               isset($response['message']) && 
               isset($response['data']);
    }
    
    /**
     * 生成测试图片URL
     * @param string $dataId
     * @param int $width
     * @param int $height
     * @return string
     */
    public static function generateTestImageUrl($dataId, $width = 1242, $height = 2208)
    {
        return "http://localhost:7001/static/generated/{$dataId}_{$width}x{$height}.jpg";
    }
}
