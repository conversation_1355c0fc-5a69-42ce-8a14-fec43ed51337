-- 动态参数模板系统数据库索引优化脚本
-- 创建时间: 2025-01-16
-- 描述: 为提高查询性能添加必要的索引

SET NAMES utf8mb4;

-- ============================================================================
-- 1. 模板配置表索引优化
-- ============================================================================

-- 基础索引（已在建表时创建）
-- PRIMARY KEY (`id`)
-- INDEX `idx_template_id` (`template_id`)
-- INDEX `idx_created_by` (`created_by`)
-- INDEX `idx_status` (`status`)
-- INDEX `idx_created_at` (`created_at`)

-- 复合索引优化
ALTER TABLE `ls_poster_template_configs` 
ADD INDEX `idx_template_status` (`template_id`, `status`),
ADD INDEX `idx_status_created` (`status`, `created_at`),
ADD INDEX `idx_created_by_status` (`created_by`, `status`);

-- JSON字段虚拟列索引（MySQL 5.7+）
-- 添加参数数量虚拟列
ALTER TABLE `ls_poster_template_configs` 
ADD COLUMN `parameter_count` INT GENERATED ALWAYS AS (JSON_LENGTH(parameters)) VIRTUAL,
ADD INDEX `idx_parameter_count` (`parameter_count`);

-- 添加启用参数数量虚拟列（需要更复杂的JSON查询，暂时注释）
-- ALTER TABLE `ls_poster_template_configs` 
-- ADD COLUMN `enabled_parameter_count` INT GENERATED ALWAYS AS (
--     JSON_LENGTH(JSON_EXTRACT(parameters, '$[*].isEnabled[?(@ == true)]'))
-- ) VIRTUAL,
-- ADD INDEX `idx_enabled_parameter_count` (`enabled_parameter_count`);

-- ============================================================================
-- 2. 用户数据表索引优化
-- ============================================================================

-- 基础索引（已在建表时创建）
-- PRIMARY KEY (`id`)
-- INDEX `idx_config_id` (`config_id`)
-- INDEX `idx_user_id` (`user_id`)
-- INDEX `idx_session_id` (`session_id`)
-- INDEX `idx_is_draft` (`is_draft`)
-- INDEX `idx_created_at` (`created_at`)
-- INDEX `idx_user_draft` (`user_id`, `is_draft`)
-- INDEX `idx_session_draft` (`session_id`, `is_draft`)

-- 复合索引优化
ALTER TABLE `ls_poster_user_data` 
ADD INDEX `idx_config_draft` (`config_id`, `is_draft`),
ADD INDEX `idx_config_created` (`config_id`, `created_at`),
ADD INDEX `idx_user_created` (`user_id`, `created_at`),
ADD INDEX `idx_session_created` (`session_id`, `created_at`),
ADD INDEX `idx_draft_updated` (`is_draft`, `updated_at`);

-- 覆盖索引（包含常用查询字段）
ALTER TABLE `ls_poster_user_data` 
ADD INDEX `idx_user_draft_cover` (`user_id`, `is_draft`, `config_id`, `updated_at`),
ADD INDEX `idx_session_draft_cover` (`session_id`, `is_draft`, `config_id`, `updated_at`);

-- ============================================================================
-- 3. 生成记录表索引优化
-- ============================================================================

-- 基础索引（已在建表时创建）
-- PRIMARY KEY (`id`)
-- INDEX `idx_data_id` (`data_id`)
-- INDEX `idx_created_at` (`created_at`)
-- INDEX `idx_status` (`status`)
-- INDEX `idx_generation_time` (`generation_time`)
-- INDEX `idx_file_size` (`file_size`)
-- INDEX `idx_data_status` (`data_id`, `status`)

-- 复合索引优化
ALTER TABLE `ls_poster_generation_records` 
ADD INDEX `idx_status_created` (`status`, `created_at`),
ADD INDEX `idx_status_time` (`status`, `generation_time`),
ADD INDEX `idx_status_size` (`status`, `file_size`),
ADD INDEX `idx_data_created` (`data_id`, `created_at`);

-- 统计查询优化索引
ALTER TABLE `ls_poster_generation_records` 
ADD INDEX `idx_size_dimensions` (`image_width`, `image_height`, `status`),
ADD INDEX `idx_format_status` (`image_format`, `status`),
ADD INDEX `idx_quality_status` (`quality`, `status`);

-- 时间范围查询优化
ALTER TABLE `ls_poster_generation_records` 
ADD INDEX `idx_created_status_time` (`created_at`, `status`, `generation_time`);

-- 虚拟列索引
ALTER TABLE `ls_poster_generation_records` 
ADD COLUMN `size_mb` DECIMAL(10,2) GENERATED ALWAYS AS (file_size / 1048576) VIRTUAL,
ADD INDEX `idx_size_mb` (`size_mb`),
ADD INDEX `idx_size_mb_status` (`size_mb`, `status`);

-- ============================================================================
-- 4. 全文搜索索引（可选）
-- ============================================================================

-- 为模板配置表添加全文搜索索引
ALTER TABLE `ls_poster_template_configs` 
ADD FULLTEXT INDEX `ft_config_search` (`config_name`, `config_description`, `template_title`);

-- ============================================================================
-- 5. 分区表优化（可选，适用于大数据量）
-- ============================================================================

-- 注意：分区表需要在建表时定义，这里仅作为示例
-- 按月分区生成记录表（适用于数据量很大的情况）
/*
ALTER TABLE `ls_poster_generation_records` 
PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    PARTITION p202503 VALUES LESS THAN (202504),
    PARTITION p202504 VALUES LESS THAN (202505),
    PARTITION p202505 VALUES LESS THAN (202506),
    PARTITION p202506 VALUES LESS THAN (202507),
    PARTITION p202507 VALUES LESS THAN (202508),
    PARTITION p202508 VALUES LESS THAN (202509),
    PARTITION p202509 VALUES LESS THAN (202510),
    PARTITION p202510 VALUES LESS THAN (202511),
    PARTITION p202511 VALUES LESS THAN (202512),
    PARTITION p202512 VALUES LESS THAN (202601),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
*/

-- ============================================================================
-- 6. 索引使用建议和查询优化
-- ============================================================================

-- 查看索引使用情况的SQL
/*
-- 查看表的索引信息
SHOW INDEX FROM ls_poster_template_configs;
SHOW INDEX FROM ls_poster_user_data;
SHOW INDEX FROM ls_poster_generation_records;

-- 分析表统计信息
ANALYZE TABLE ls_poster_template_configs;
ANALYZE TABLE ls_poster_user_data;
ANALYZE TABLE ls_poster_generation_records;

-- 查看索引基数
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    CARDINALITY
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('ls_poster_template_configs', 'ls_poster_user_data', 'ls_poster_generation_records')
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;
*/

-- 显示优化结果
SELECT 'Poster Template System Index Optimization Completed!' as Status;

-- 显示各表的索引数量
SELECT 
    TABLE_NAME,
    COUNT(*) as INDEX_COUNT
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('ls_poster_template_configs', 'ls_poster_user_data', 'ls_poster_generation_records')
GROUP BY TABLE_NAME;
