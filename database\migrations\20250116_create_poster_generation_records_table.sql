-- 动态参数模板系统 - 图片生成记录表
-- 创建时间: 2025-01-16
-- 描述: 存储图片生成的历史记录和统计信息

CREATE TABLE IF NOT EXISTS `ls_poster_generation_records` (
  `id` VARCHAR(32) NOT NULL COMMENT '记录ID',
  `data_id` VARCHAR(32) NOT NULL COMMENT '参数数据ID',
  `image_url` VARCHAR(500) NOT NULL COMMENT '生成的图片URL',
  `generation_options` JSON NULL COMMENT '生成选项（宽度、高度、质量等）',
  `generation_time` DECIMAL(10,3) NULL COMMENT '生成耗时（秒）',
  `file_size` INT NULL COMMENT '文件大小（字节）',
  `image_width` INT NULL COMMENT '图片宽度',
  `image_height` INT NULL COMMENT '图片高度',
  `image_format` VARCHAR(10) NULL COMMENT '图片格式（jpg、png等）',
  `quality` DECIMAL(3,2) NULL COMMENT '图片质量（0.1-1.0）',
  `status` TINYINT(1) DEFAULT 1 COMMENT '状态：1成功 0失败',
  `error_message` TEXT NULL COMMENT '错误信息（如果生成失败）',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  PRIMARY KEY (`id`),
  INDEX `idx_data_id` (`data_id`),
  INDEX `idx_created_at` (`created_at`),
  INDEX `idx_status` (`status`),
  INDEX `idx_generation_time` (`generation_time`),
  INDEX `idx_file_size` (`file_size`),
  INDEX `idx_data_status` (`data_id`, `status`),
  
  CONSTRAINT `fk_poster_generation_records_data` 
    FOREIGN KEY (`data_id`) 
    REFERENCES `ls_poster_user_data`(`id`) 
    ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片生成记录表';

-- 添加生成统计的虚拟列
-- ALTER TABLE `ls_poster_generation_records` 
-- ADD COLUMN `size_mb` DECIMAL(10,2) GENERATED ALWAYS AS (file_size / 1048576) VIRTUAL COMMENT '文件大小（MB）',
-- ADD INDEX `idx_size_mb` (`size_mb`);
