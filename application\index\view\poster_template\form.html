<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$config.config_name} - 个性化海报制作</title>
    <link rel="stylesheet" href="/static/layui/css/layui.css">
    <style>
        body { background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .form-container { background: white; border-radius: 8px; padding: 30px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .preview-container { background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .template-info { margin-bottom: 30px; padding-bottom: 20px; border-bottom: 1px solid #eee; }
        .template-info h1 { margin: 0 0 10px 0; color: #333; }
        .template-info p { margin: 0; color: #666; }
        .form-section { margin-bottom: 25px; }
        .form-section h3 { margin: 0 0 15px 0; color: #333; font-size: 16px; }
        .preview-placeholder { text-align: center; padding: 60px 20px; color: #999; border: 2px dashed #ddd; border-radius: 8px; }
        .preview-image { max-width: 100%; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); }
        .action-buttons { text-align: center; margin-top: 20px; }
        .action-buttons .layui-btn { margin: 0 10px; }
        .draft-info { background: #f0f9ff; border: 1px solid #b3d8ff; border-radius: 4px; padding: 10px; margin-bottom: 20px; }
        .parameter-help { font-size: 12px; color: #999; margin-top: 5px; }
        .required-mark { color: #ff5722; }
    </style>
</head>
<body>
    <div class="container">
        <div class="layui-row layui-col-space20">
            <!-- 左侧：表单区域 -->
            <div class="layui-col-md8">
                <div class="form-container">
                    <!-- 模板信息 -->
                    <div class="template-info">
                        <h1>{$config.config_name}</h1>
                        <p>{$config.config_description}</p>
                        <p><small>模板ID: {$config.template_id} | 参数数量: {$config.parameter_stats.total}</small></p>
                    </div>
                    
                    <!-- 草稿提示 -->
                    {if $draftData}
                    <div class="draft-info">
                        <i class="layui-icon layui-icon-tips"></i>
                        检测到您有未完成的草稿，已自动填充表单数据。
                    </div>
                    {/if}
                    
                    <!-- 动态表单 -->
                    <form class="layui-form" id="poster-form" lay-filter="poster-form">
                        <input type="hidden" name="config_id" value="{$config.id}">
                        <input type="hidden" name="user_id" value="{$userId}">
                        <input type="hidden" name="session_id" value="{$sessionId}">
                        
                        <div id="form-fields">
                            <!-- 动态生成的表单字段将在这里显示 -->
                        </div>
                        
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button type="button" class="layui-btn layui-btn-primary" id="save-draft">
                                    <i class="layui-icon layui-icon-file"></i>保存草稿
                                </button>
                                <button type="button" class="layui-btn layui-btn-normal" id="generate-preview">
                                    <i class="layui-icon layui-icon-survey"></i>生成预览
                                </button>
                                <button type="button" class="layui-btn" id="generate-image" disabled>
                                    <i class="layui-icon layui-icon-picture"></i>生成图片
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- 右侧：预览区域 -->
            <div class="layui-col-md4">
                <div class="preview-container">
                    <h3>预览效果</h3>
                    <div id="preview-area">
                        <div class="preview-placeholder">
                            <i class="layui-icon layui-icon-picture" style="font-size: 48px;"></i>
                            <p>填写参数后点击"生成预览"查看效果</p>
                        </div>
                    </div>
                    
                    <div class="action-buttons" id="preview-actions" style="display: none;">
                        <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="refresh-preview">
                            <i class="layui-icon layui-icon-refresh"></i>刷新预览
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 图片生成选项弹窗 -->
    <div id="image-options-modal" style="display: none; padding: 20px;">
        <form class="layui-form" id="image-options-form">
            <div class="layui-form-item">
                <label class="layui-form-label">图片宽度</label>
                <div class="layui-input-block">
                    <input type="number" name="width" value="1242" placeholder="请输入图片宽度" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">图片高度</label>
                <div class="layui-input-block">
                    <input type="number" name="height" value="2208" placeholder="请输入图片高度" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">图片质量</label>
                <div class="layui-input-block">
                    <select name="quality">
                        <option value="0.5">低质量 (0.5)</option>
                        <option value="0.8">中等质量 (0.8)</option>
                        <option value="0.9" selected>高质量 (0.9)</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">输出格式</label>
                <div class="layui-input-block">
                    <select name="type">
                        <option value="file" selected>文件</option>
                        <option value="base64">Base64</option>
                    </select>
                </div>
            </div>
        </form>
    </div>

    <script src="/static/layui/layui.js"></script>
    <script>
        layui.use(['form', 'layer', 'element'], function(){
            var form = layui.form;
            var layer = layui.layer;
            var element = layui.element;
            
            // 配置数据
            var config = {$config|json_encode};
            var draftData = {$draftData|json_encode} || {};
            var currentDataId = null;
            
            // 初始化表单
            initForm();
            
            // 初始化表单
            function initForm() {
                var parameters = config.parameters || [];
                var formHtml = '';
                
                // 按显示顺序排序
                parameters.sort(function(a, b) {
                    return (a.displayOrder || 0) - (b.displayOrder || 0);
                });
                
                parameters.forEach(function(param) {
                    if (!param.isEnabled) return;
                    
                    formHtml += generateFormField(param);
                });
                
                document.getElementById('form-fields').innerHTML = formHtml;
                
                // 填充草稿数据
                if (draftData) {
                    fillFormData(draftData);
                }
                
                form.render();
            }
            
            // 生成表单字段
            function generateFormField(param) {
                var value = draftData[param.parameterName] || param.defaultValue || '';
                var required = param.isRequired ? 'required lay-verify="required"' : '';
                var requiredMark = param.isRequired ? '<span class="required-mark">*</span>' : '';
                
                var html = '<div class="form-section">';
                html += '<h3>' + param.parameterLabel + requiredMark + '</h3>';
                
                switch (param.parameterType) {
                    case 'textarea':
                        html += '<div class="layui-form-item">';
                        html += '<div class="layui-input-block">';
                        html += '<textarea name="' + param.parameterName + '" placeholder="' + (param.parameterDescription || '请输入' + param.parameterLabel) + '" class="layui-textarea" ' + required + '>' + value + '</textarea>';
                        html += '</div>';
                        html += '</div>';
                        break;
                        
                    case 'email':
                        html += '<div class="layui-form-item">';
                        html += '<div class="layui-input-block">';
                        html += '<input type="email" name="' + param.parameterName + '" value="' + value + '" placeholder="' + (param.parameterDescription || '请输入邮箱地址') + '" class="layui-input" ' + required + '>';
                        html += '</div>';
                        html += '</div>';
                        break;
                        
                    case 'phone':
                        html += '<div class="layui-form-item">';
                        html += '<div class="layui-input-block">';
                        html += '<input type="tel" name="' + param.parameterName + '" value="' + value + '" placeholder="' + (param.parameterDescription || '请输入手机号码') + '" class="layui-input" ' + required + '>';
                        html += '</div>';
                        html += '</div>';
                        break;
                        
                    case 'number':
                        html += '<div class="layui-form-item">';
                        html += '<div class="layui-input-block">';
                        html += '<input type="number" name="' + param.parameterName + '" value="' + value + '" placeholder="' + (param.parameterDescription || '请输入数字') + '" class="layui-input" ' + required + '>';
                        html += '</div>';
                        html += '</div>';
                        break;
                        
                    case 'date':
                        html += '<div class="layui-form-item">';
                        html += '<div class="layui-input-block">';
                        html += '<input type="text" name="' + param.parameterName + '" value="' + value + '" placeholder="' + (param.parameterDescription || '请选择日期') + '" class="layui-input" id="date-' + param.parameterName + '" ' + required + '>';
                        html += '</div>';
                        html += '</div>';
                        break;
                        
                    default: // text
                        html += '<div class="layui-form-item">';
                        html += '<div class="layui-input-block">';
                        html += '<input type="text" name="' + param.parameterName + '" value="' + value + '" placeholder="' + (param.parameterDescription || '请输入' + param.parameterLabel) + '" class="layui-input" ' + required + '>';
                        html += '</div>';
                        html += '</div>';
                        break;
                }
                
                if (param.parameterDescription) {
                    html += '<div class="parameter-help">' + param.parameterDescription + '</div>';
                }
                
                html += '</div>';
                
                return html;
            }
            
            // 填充表单数据
            function fillFormData(data) {
                for (var key in data) {
                    var input = document.querySelector('[name="' + key + '"]');
                    if (input) {
                        input.value = data[key];
                    }
                }
            }
            
            // 获取表单数据
            function getFormData() {
                var formData = {};
                var inputs = document.querySelectorAll('#poster-form input, #poster-form textarea, #poster-form select');
                
                inputs.forEach(function(input) {
                    if (input.name && input.name !== 'config_id' && input.name !== 'user_id' && input.name !== 'session_id') {
                        formData[input.name] = input.value;
                    }
                });
                
                return formData;
            }
            
            // 保存草稿
            document.getElementById('save-draft').addEventListener('click', function() {
                var formData = getFormData();
                
                var data = {
                    config_id: config.id,
                    parameter_values: formData,
                    is_draft: true
                };
                
                var loadingIndex = layer.load(2, {content: '正在保存草稿...'});
                
                $.post('/index/poster_template/save_data', data, function(res) {
                    layer.close(loadingIndex);
                    
                    if (res.code == 1) {
                        currentDataId = res.data.id;
                        layer.msg('草稿保存成功', {icon: 1});
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }).fail(function() {
                    layer.close(loadingIndex);
                    layer.msg('网络错误，请重试', {icon: 2});
                });
            });
            
            // 生成预览
            document.getElementById('generate-preview').addEventListener('click', function() {
                // 先保存数据
                var formData = getFormData();
                
                var data = {
                    config_id: config.id,
                    parameter_values: formData,
                    is_draft: false
                };
                
                var loadingIndex = layer.load(2, {content: '正在生成预览...'});
                
                $.post('/index/poster_template/save_data', data, function(res) {
                    if (res.code == 1) {
                        currentDataId = res.data.id;
                        
                        // 生成预览
                        $.post('/index/poster_template/generate_preview', {data_id: currentDataId}, function(previewRes) {
                            layer.close(loadingIndex);
                            
                            if (previewRes.code == 1) {
                                showPreview(previewRes.data.previewUrl);
                                document.getElementById('generate-image').disabled = false;
                                layer.msg('预览生成成功', {icon: 1});
                            } else {
                                layer.msg(previewRes.msg, {icon: 2});
                            }
                        }).fail(function() {
                            layer.close(loadingIndex);
                            layer.msg('预览生成失败', {icon: 2});
                        });
                    } else {
                        layer.close(loadingIndex);
                        layer.msg(res.msg, {icon: 2});
                    }
                }).fail(function() {
                    layer.close(loadingIndex);
                    layer.msg('网络错误，请重试', {icon: 2});
                });
            });
            
            // 显示预览
            function showPreview(previewUrl) {
                var previewHtml = '<iframe src="' + previewUrl + '" frameborder="0" style="width:100%;height:400px;border-radius:8px;"></iframe>';
                document.getElementById('preview-area').innerHTML = previewHtml;
                document.getElementById('preview-actions').style.display = 'block';
            }
            
            // 生成图片
            document.getElementById('generate-image').addEventListener('click', function() {
                if (!currentDataId) {
                    layer.msg('请先生成预览', {icon: 0});
                    return;
                }
                
                layer.open({
                    type: 1,
                    title: '图片生成选项',
                    area: ['500px', '400px'],
                    content: document.getElementById('image-options-modal').innerHTML,
                    btn: ['生成图片', '取消'],
                    yes: function(index) {
                        var options = {};
                        var inputs = layer.getChildFrame('body', index).find('#image-options-form input, #image-options-form select');
                        
                        inputs.each(function() {
                            options[this.name] = this.value;
                        });
                        
                        options.data_id = currentDataId;
                        
                        layer.close(index);
                        
                        var loadingIndex = layer.load(2, {content: '正在生成图片...'});
                        
                        $.post('/index/poster_template/generate_image', options, function(res) {
                            layer.close(loadingIndex);
                            
                            if (res.code == 1) {
                                showGeneratedImage(res.data);
                            } else {
                                layer.msg(res.msg, {icon: 2});
                            }
                        }).fail(function() {
                            layer.close(loadingIndex);
                            layer.msg('图片生成失败', {icon: 2});
                        });
                    }
                });
            });
            
            // 显示生成的图片
            function showGeneratedImage(imageData) {
                var content = '<div style="text-align:center;">' +
                             '<img src="' + imageData.imageUrl + '" style="max-width:100%;max-height:500px;border-radius:8px;">' +
                             '<div style="margin-top:15px;color:#666;">' +
                             '<p>文件大小: ' + formatFileSize(imageData.fileSize) + '</p>' +
                             '<p>生成时间: ' + imageData.generationTime + ' 秒</p>' +
                             '</div>' +
                             '</div>';
                
                layer.open({
                    type: 1,
                    title: '生成完成',
                    area: ['70%', '80%'],
                    content: content,
                    btn: ['下载图片', '关闭'],
                    yes: function(index) {
                        window.open(imageData.imageUrl);
                    }
                });
            }
            
            // 格式化文件大小
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 B';
                var k = 1024;
                var sizes = ['B', 'KB', 'MB', 'GB'];
                var i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }
        });
    </script>
</body>
</html>
