# 动态参数模板系统测试目录

本目录包含动态参数模板系统的所有测试文件。

## 目录结构

```
test/poster_template/
├── README.md                    # 本文件
├── config/                      # 测试配置
│   ├── database.php            # 测试数据库配置
│   ├── api.php                 # API测试配置
│   └── mock.php                # Mock数据配置
├── unit/                       # 单元测试
│   ├── model/                  # 模型测试
│   ├── logic/                  # 业务逻辑测试
│   ├── service/                # 服务层测试
│   └── api/                    # API测试
├── integration/                # 集成测试
│   ├── admin/                  # 后台管理集成测试
│   ├── api/                    # API集成测试
│   └── workflow/               # 工作流测试
├── functional/                 # 功能测试
│   ├── template_parse/         # 模板解析功能测试
│   ├── parameter_config/       # 参数配置功能测试
│   ├── preview_generate/       # 预览生成功能测试
│   └── image_generate/         # 图片生成功能测试
├── data/                       # 测试数据
│   ├── fixtures/               # 固定测试数据
│   ├── mock/                   # Mock数据
│   └── samples/                # 示例数据
└── tools/                      # 测试工具
    ├── helpers/                # 测试辅助函数
    ├── factories/              # 数据工厂
    └── seeders/                # 数据种子
```

## 测试规范

1. 所有测试文件必须以 `Test.php` 结尾
2. 测试类必须继承相应的基础测试类
3. 测试方法必须以 `test` 开头
4. 每个测试方法应该只测试一个功能点
5. 使用描述性的测试方法名称

## 运行测试

```bash
# 运行所有测试
php think test poster_template

# 运行单元测试
php think test poster_template/unit

# 运行集成测试
php think test poster_template/integration

# 运行功能测试
php think test poster_template/functional
```
